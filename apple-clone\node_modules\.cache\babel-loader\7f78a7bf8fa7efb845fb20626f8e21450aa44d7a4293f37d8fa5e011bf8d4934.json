{"ast": null, "code": "import { getDevtoolsGlobalHook, getTarget, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n  const descriptor = pluginDescriptor;\n  const target = getTarget();\n  const hook = getDevtoolsGlobalHook();\n  const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n  if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n    hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n  } else {\n    const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n    const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n    list.push({\n      pluginDescriptor: descriptor,\n      setupFn,\n      proxy\n    });\n    if (proxy) {\n      setupFn(proxy.proxiedTarget);\n    }\n  }\n}", "map": {"version": 3, "names": ["getDevtoolsGlobalHook", "get<PERSON><PERSON><PERSON>", "isProxyAvailable", "HOOK_SETUP", "ApiProxy", "setupDevtoolsPlugin", "pluginDescriptor", "setupFn", "descriptor", "target", "hook", "enableProxy", "enableEarlyProxy", "__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__", "emit", "proxy", "list", "__VUE_DEVTOOLS_PLUGINS__", "push", "proxied<PERSON><PERSON><PERSON>"], "sources": ["E:/cursor/apple-clone/node_modules/@vue/devtools-api/lib/esm/index.js"], "sourcesContent": ["import { getDevtoolsGlobalHook, getTarget, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n    const descriptor = pluginDescriptor;\n    const target = getTarget();\n    const hook = getDevtoolsGlobalHook();\n    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n        hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n    }\n    else {\n        const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n        const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n        list.push({\n            pluginDescriptor: descriptor,\n            setupFn,\n            proxy,\n        });\n        if (proxy) {\n            setupFn(proxy.proxiedTarget);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,UAAU;AAC7E,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,QAAQ,QAAQ,YAAY;AACrC,cAAc,gBAAgB;AAC9B,cAAc,aAAa;AAC3B,cAAc,WAAW;AACzB,OAAO,SAASC,mBAAmBA,CAACC,gBAAgB,EAAEC,OAAO,EAAE;EAC3D,MAAMC,UAAU,GAAGF,gBAAgB;EACnC,MAAMG,MAAM,GAAGR,SAAS,CAAC,CAAC;EAC1B,MAAMS,IAAI,GAAGV,qBAAqB,CAAC,CAAC;EACpC,MAAMW,WAAW,GAAGT,gBAAgB,IAAIM,UAAU,CAACI,gBAAgB;EACnE,IAAIF,IAAI,KAAKD,MAAM,CAACI,qCAAqC,IAAI,CAACF,WAAW,CAAC,EAAE;IACxED,IAAI,CAACI,IAAI,CAACX,UAAU,EAAEG,gBAAgB,EAAEC,OAAO,CAAC;EACpD,CAAC,MACI;IACD,MAAMQ,KAAK,GAAGJ,WAAW,GAAG,IAAIP,QAAQ,CAACI,UAAU,EAAEE,IAAI,CAAC,GAAG,IAAI;IACjE,MAAMM,IAAI,GAAGP,MAAM,CAACQ,wBAAwB,GAAGR,MAAM,CAACQ,wBAAwB,IAAI,EAAE;IACpFD,IAAI,CAACE,IAAI,CAAC;MACNZ,gBAAgB,EAAEE,UAAU;MAC5BD,OAAO;MACPQ;IACJ,CAAC,CAAC;IACF,IAAIA,KAAK,EAAE;MACPR,OAAO,CAACQ,KAAK,CAACI,aAAa,CAAC;IAChC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}