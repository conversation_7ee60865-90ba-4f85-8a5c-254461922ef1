import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/store',
    name: 'Store',
    component: () => import('../views/Store.vue')
  },
  {
    path: '/mac',
    name: 'Mac',
    component: () => import('../views/Mac.vue')
  },
  {
    path: '/ipad',
    name: 'iPad',
    component: () => import('../views/iPad.vue')
  },
  {
    path: '/iphone',
    name: 'iPhone',
    component: () => import('../views/iPhone.vue')
  },
  {
    path: '/watch',
    name: 'Watch',
    component: () => import('../views/Watch.vue')
  },
  {
    path: '/airpods',
    name: 'AirPods',
    component: () => import('../views/AirPods.vue')
  },
  {
    path: '/tv-home',
    name: 'TVHome',
    component: () => import('../views/TVHome.vue')
  },
  {
    path: '/entertainment',
    name: 'Entertainment',
    component: () => import('../views/Entertainment.vue')
  },
  {
    path: '/accessories',
    name: 'Accessories',
    component: () => import('../views/Accessories.vue')
  },
  {
    path: '/support',
    name: 'Support',
    component: () => import('../views/Support.vue')
  },
  {
    path: '/bag',
    name: 'Bag',
    component: () => import('../views/Bag.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router 