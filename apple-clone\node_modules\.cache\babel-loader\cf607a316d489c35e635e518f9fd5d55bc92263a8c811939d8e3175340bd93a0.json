{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"support\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = {\n  class: \"cta-links\"\n};\nconst _hoisted_5 = {\n  class: \"product-grid\"\n};\nconst _hoisted_6 = {\n  class: \"product-item\"\n};\nconst _hoisted_7 = {\n  class: \"cta-links\"\n};\nconst _hoisted_8 = {\n  class: \"product-item\"\n};\nconst _hoisted_9 = {\n  class: \"cta-links\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"h1\", null, \"技术支持\", -1 /* HOISTED */)), _cache[3] || (_cache[3] = _createElementVNode(\"h2\", null, \"我们随时为您提供帮助。\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_router_link, {\n    to: \"/support/contact\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"联系我们\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }), _createVNode(_component_router_link, {\n    to: \"/support/community\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"社区\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  })])]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"hero-image\"\n  }, [_createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/600x400/F5F5F7/000000?text=Support\",\n    alt: \"Support\"\n  })], -1 /* HOISTED */))]), _createElementVNode(\"section\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"获取支持\", -1 /* HOISTED */)), _cache[8] || (_cache[8] = _createElementVNode(\"p\", null, \"查找答案和解决方案\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_router_link, {\n    to: \"/support/get-help\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [5]\n  }), _createVNode(_component_router_link, {\n    to: \"/support/contact\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"联系我们\")])),\n    _: 1 /* STABLE */,\n    __: [6]\n  })]), _cache[9] || (_cache[9] = _createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/400x300/F5F5F7/000000?text=Get+Support\",\n    alt: \"获取支持\"\n  }, null, -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_8, [_cache[12] || (_cache[12] = _createElementVNode(\"h3\", null, \"用户手册\", -1 /* HOISTED */)), _cache[13] || (_cache[13] = _createElementVNode(\"p\", null, \"产品使用指南\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_router_link, {\n    to: \"/support/manuals\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [10]\n  }), _createVNode(_component_router_link, {\n    to: \"/support/downloads\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"下载\")])),\n    _: 1 /* STABLE */,\n    __: [11]\n  })]), _cache[14] || (_cache[14] = _createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/400x300/F5F5F7/000000?text=Manuals\",\n    alt: \"用户手册\"\n  }, null, -1 /* HOISTED */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_router_link", "to", "_cache", "src", "alt", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Support.vue"], "sourcesContent": ["<template>\n  <div class=\"support\">\n    <section class=\"hero\">\n      <div class=\"hero-content\">\n        <h1>技术支持</h1>\n        <h2>我们随时为您提供帮助。</h2>\n        <div class=\"cta-links\">\n          <router-link to=\"/support/contact\" class=\"link\">联系我们</router-link>\n          <router-link to=\"/support/community\" class=\"link\">社区</router-link>\n        </div>\n      </div>\n      <div class=\"hero-image\">\n        <img src=\"https://via.placeholder.com/600x400/F5F5F7/000000?text=Support\" alt=\"Support\" />\n      </div>\n    </section>\n\n    <section class=\"product-grid\">\n      <div class=\"product-item\">\n        <h3>获取支持</h3>\n        <p>查找答案和解决方案</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/support/get-help\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/support/contact\" class=\"link\">联系我们</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=Get+Support\" alt=\"获取支持\" />\n      </div>\n\n      <div class=\"product-item\">\n        <h3>用户手册</h3>\n        <p>产品使用指南</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/support/manuals\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/support/downloads\" class=\"link\">下载</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=Manuals\" alt=\"用户手册\" />\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Support'\n}\n</script>\n\n<style scoped>\n.support {\n  padding-top: 44px;\n}\n\n.hero {\n  background-color: #f5f5f7;\n  color: #1d1d1f;\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.hero-content {\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.hero h1 {\n  font-size: 56px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.hero h2 {\n  font-size: 28px;\n  font-weight: 400;\n  margin-bottom: 20px;\n}\n\n.cta-links {\n  display: flex;\n  justify-content: center;\n  gap: 35px;\n  margin-bottom: 20px;\n}\n\n.link {\n  color: #2997ff;\n  text-decoration: none;\n  font-size: 21px;\n}\n\n.link:hover {\n  text-decoration: underline;\n}\n\n.hero-image img {\n  max-width: 100%;\n  height: auto;\n}\n\n.product-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20px;\n  padding: 20px;\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.product-item {\n  background-color: #fbfbfd;\n  text-align: center;\n  padding: 40px 20px;\n  border-radius: 18px;\n}\n\n.product-item h3 {\n  font-size: 40px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.product-item p {\n  font-size: 21px;\n  margin-bottom: 20px;\n}\n\n.product-item img {\n  max-width: 100%;\n  height: auto;\n}\n\n@media (max-width: 768px) {\n  .product-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .hero h1 {\n    font-size: 40px;\n  }\n\n  .hero h2 {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAS;;EACTA,KAAK,EAAC;AAAM;;EACdA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;EAUjBA,KAAK,EAAC;AAAc;;EACtBA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;;uBA7B5BC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJC,mBAAA,CAYU,WAZVC,UAYU,GAXRD,mBAAA,CAOM,OAPNE,UAOM,G,0BANJF,mBAAA,CAAa,YAAT,MAAI,sB,0BACRA,mBAAA,CAAoB,YAAhB,aAAW,sBACfA,mBAAA,CAGM,OAHNG,UAGM,GAFJC,YAAA,CAAkEC,sBAAA;IAArDC,EAAE,EAAC,kBAAkB;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MACpDH,YAAA,CAAkEC,sBAAA;IAArDC,EAAE,EAAC,oBAAoB;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;oCAGxDP,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAA0F;IAArFQ,GAAG,EAAC,gEAAgE;IAACC,GAAG,EAAC;6BAIlFT,mBAAA,CAoBU,WApBVU,UAoBU,GAnBRV,mBAAA,CAQM,OARNW,UAQM,G,0BAPJX,mBAAA,CAAa,YAAT,MAAI,sB,0BACRA,mBAAA,CAAgB,WAAb,WAAS,sBACZA,mBAAA,CAGM,OAHNY,UAGM,GAFJR,YAAA,CAAmEC,sBAAA;IAAtDC,EAAE,EAAC,mBAAmB;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MACrDH,YAAA,CAAkEC,sBAAA;IAArDC,EAAE,EAAC,kBAAkB;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;kCAEtDP,mBAAA,CAA2F;IAAtFQ,GAAG,EAAC,oEAAoE;IAACC,GAAG,EAAC;iCAGpFT,mBAAA,CAQM,OARNa,UAQM,G,4BAPJb,mBAAA,CAAa,YAAT,MAAI,sB,4BACRA,mBAAA,CAAa,WAAV,QAAM,sBACTA,mBAAA,CAGM,OAHNc,UAGM,GAFJV,YAAA,CAAkEC,sBAAA;IAArDC,EAAE,EAAC,kBAAkB;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;MACpDH,YAAA,CAAkEC,sBAAA;IAArDC,EAAE,EAAC,oBAAoB;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;oCAEtDP,mBAAA,CAAuF;IAAlFQ,GAAG,EAAC,gEAAgE;IAACC,GAAG,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}