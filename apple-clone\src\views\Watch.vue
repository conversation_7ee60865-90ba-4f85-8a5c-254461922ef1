<template>
  <div class="watch">
    <section class="hero">
      <div class="hero-content">
        <h1>Apple Watch</h1>
        <h2>健康的未来，现在戴上。</h2>
        <div class="cta-links">
          <router-link to="/watch/series-9" class="link">了解更多</router-link>
          <router-link to="/shop/watch" class="link">购买</router-link>
        </div>
      </div>
      <div class="hero-image">
        <img src="https://via.placeholder.com/600x400/F5F5F7/000000?text=Apple+Watch" alt="Apple Watch" />
      </div>
    </section>

    <section class="product-grid">
      <div class="product-item">
        <h3>Apple Watch Series 9</h3>
        <p>更智能。更亮眼。更强大。</p>
        <div class="cta-links">
          <router-link to="/watch-series-9" class="link">了解更多</router-link>
          <router-link to="/shop/watch-series-9" class="link">购买</router-link>
        </div>
        <img src="https://via.placeholder.com/400x300/F5F5F7/000000?text=Series+9" alt="Apple Watch Series 9" />
      </div>

      <div class="product-item">
        <h3>Apple Watch SE</h3>
        <p>功能丰富。价格亲民。</p>
        <div class="cta-links">
          <router-link to="/watch-se" class="link">了解更多</router-link>
          <router-link to="/shop/watch-se" class="link">购买</router-link>
        </div>
        <img src="https://via.placeholder.com/400x300/F5F5F7/000000?text=Watch+SE" alt="Apple Watch SE" />
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Watch'
}
</script>

<style scoped>
.watch {
  padding-top: 44px;
}

.hero {
  background-color: #f5f5f7;
  color: #1d1d1f;
  text-align: center;
  padding: 60px 20px;
}

.hero-content {
  max-width: 1024px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 56px;
  font-weight: 600;
  margin-bottom: 10px;
}

.hero h2 {
  font-size: 28px;
  font-weight: 400;
  margin-bottom: 20px;
}

.cta-links {
  display: flex;
  justify-content: center;
  gap: 35px;
  margin-bottom: 20px;
}

.link {
  color: #2997ff;
  text-decoration: none;
  font-size: 21px;
}

.link:hover {
  text-decoration: underline;
}

.hero-image img {
  max-width: 100%;
  height: auto;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px;
  max-width: 1024px;
  margin: 0 auto;
}

.product-item {
  background-color: #fbfbfd;
  text-align: center;
  padding: 40px 20px;
  border-radius: 18px;
}

.product-item h3 {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: 10px;
}

.product-item p {
  font-size: 21px;
  margin-bottom: 20px;
}

.product-item img {
  max-width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr;
  }

  .hero h1 {
    font-size: 40px;
  }

  .hero h2 {
    font-size: 24px;
  }
}
</style>
