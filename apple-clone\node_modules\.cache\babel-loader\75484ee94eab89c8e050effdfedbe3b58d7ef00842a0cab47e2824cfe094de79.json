{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"store-page\"\n};\nconst _hoisted_2 = {\n  class: \"store-container\"\n};\nconst _hoisted_3 = {\n  class: \"products-grid\"\n};\nconst _hoisted_4 = [\"src\", \"alt\"];\nconst _hoisted_5 = {\n  class: \"product-description\"\n};\nconst _hoisted_6 = {\n  class: \"product-price\"\n};\nconst _hoisted_7 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"Apple Store 在线商店\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.products, product => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: product.id,\n      class: \"product-card\"\n    }, [_createElementVNode(\"img\", {\n      src: product.image,\n      alt: product.name,\n      class: \"product-image\"\n    }, null, 8 /* PROPS */, _hoisted_4), _createElementVNode(\"h3\", null, _toDisplayString(product.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_5, _toDisplayString(product.description), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_6, \"¥\" + _toDisplayString(product.price), 1 /* TEXT */), _createElementVNode(\"button\", {\n      class: \"buy-button\",\n      onClick: $event => _ctx.addToCart(product)\n    }, \"购买\", 8 /* PROPS */, _hoisted_7)]);\n  }), 128 /* KEYED_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_Fragment", "_renderList", "$data", "products", "product", "key", "id", "src", "image", "alt", "name", "_toDisplayString", "_hoisted_5", "description", "_hoisted_6", "price", "onClick", "$event", "_ctx", "addToCart", "_hoisted_7"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Store.vue"], "sourcesContent": ["<template>\r\n  <div class=\"store-page\">\r\n    <div class=\"store-container\">\r\n      <h1>Apple Store 在线商店</h1>\r\n      <div class=\"products-grid\">\r\n        <div v-for=\"product in products\" :key=\"product.id\" class=\"product-card\">\r\n          <img :src=\"product.image\" :alt=\"product.name\" class=\"product-image\" />\r\n          <h3>{{ product.name }}</h3>\r\n          <p class=\"product-description\">{{ product.description }}</p>\r\n          <p class=\"product-price\">¥{{ product.price }}</p>\r\n          <button class=\"buy-button\" @click=\"addToCart(product)\">购买</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { products } from '../data/products'\r\nimport { mapMutations } from 'vuex'\r\n\r\nexport default {\r\n  name: 'Store',\r\n  data() {\r\n    return {\r\n      products\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapMutations(['addToCart'])\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.store-page {\r\n  padding-top: 44px;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f7;\r\n}\r\n\r\n.store-container {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n  padding: 40px 20px;\r\n}\r\n\r\nh1 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.products-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 30px;\r\n}\r\n\r\n.product-card {\r\n  background: #fff;\r\n  border-radius: 18px;\r\n  padding: 20px;\r\n  text-align: center;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.product-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.product-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: contain;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.product-card h3 {\r\n  font-size: 24px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.product-description {\r\n  color: #86868b;\r\n  font-size: 17px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.product-price {\r\n  font-size: 21px;\r\n  font-weight: 600;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.buy-button {\r\n  width: 100%;\r\n  background: #0071e3;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 980px;\r\n  padding: 12px 24px;\r\n  font-size: 17px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.buy-button:hover {\r\n  background: #0077ed;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;;EAInBA,KAAK,EAAC;AAAqB;;EAC3BA,KAAK,EAAC;AAAe;;;uBARhCC,mBAAA,CAaM,OAbNC,UAaM,GAZJC,mBAAA,CAWM,OAXNC,UAWM,G,0BAVJD,mBAAA,CAAyB,YAArB,kBAAgB,sBACpBA,mBAAA,CAQM,OARNE,UAQM,I,kBAPJJ,mBAAA,CAMMK,SAAA,QAAAC,WAAA,CANiBC,KAAA,CAAAC,QAAQ,EAAnBC,OAAO;yBAAnBT,mBAAA,CAMM;MAN4BU,GAAG,EAAED,OAAO,CAACE,EAAE;MAAEZ,KAAK,EAAC;QACvDG,mBAAA,CAAsE;MAAhEU,GAAG,EAAEH,OAAO,CAACI,KAAK;MAAGC,GAAG,EAAEL,OAAO,CAACM,IAAI;MAAEhB,KAAK,EAAC;yCACpDG,mBAAA,CAA2B,YAAAc,gBAAA,CAApBP,OAAO,CAACM,IAAI,kBACnBb,mBAAA,CAA4D,KAA5De,UAA4D,EAAAD,gBAAA,CAA1BP,OAAO,CAACS,WAAW,kBACrDhB,mBAAA,CAAiD,KAAjDiB,UAAiD,EAAxB,GAAC,GAAAH,gBAAA,CAAGP,OAAO,CAACW,KAAK,kBAC1ClB,mBAAA,CAAkE;MAA1DH,KAAK,EAAC,YAAY;MAAEsB,OAAK,EAAAC,MAAA,IAAEC,IAAA,CAAAC,SAAS,CAACf,OAAO;OAAG,IAAE,iBAAAgB,UAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}