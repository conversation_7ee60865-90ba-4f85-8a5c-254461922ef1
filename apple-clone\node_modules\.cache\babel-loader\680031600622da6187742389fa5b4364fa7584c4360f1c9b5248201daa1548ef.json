{"ast": null, "code": "export default {\n  name: 'AnimationDemo',\n  data() {\n    return {\n      showDebug: true,\n      currentScrollRate: 0,\n      currentSection: '',\n      currentSectionRate: 0,\n      data: {\n        sections: [],\n        animateMomentInfo: {},\n        scrollHeight: 0,\n        clientHeight: 0\n      }\n    };\n  },\n  mounted() {\n    this.initScrollAnimation();\n  },\n  beforeUnmount() {\n    window.removeEventListener('scroll', this.handleScroll);\n    window.removeEventListener('resize', this.handleResize);\n  },\n  methods: {\n    // 初始化滚动动画\n    initScrollAnimation() {\n      this.data.sections = ['head', 'intro', 'tech', 'company'];\n      this.updateScrollData();\n      this.countAnimateMomentInfo();\n\n      // 绑定滚动事件\n      window.addEventListener('scroll', this.handleScroll);\n      window.addEventListener('resize', this.handleResize);\n\n      // 初始化动画状态\n      this.handleScroll();\n    },\n    // 更新滚动相关数据\n    updateScrollData() {\n      this.data.scrollHeight = document.documentElement.scrollHeight;\n      this.data.clientHeight = document.documentElement.clientHeight;\n    },\n    // 计算每个section的动画触发时机\n    countAnimateMomentInfo() {\n      for (const sectionId of this.data.sections) {\n        const node = document.getElementById(sectionId);\n        if (node) {\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node);\n        }\n      }\n    },\n    // 根据section的高度，计算它在页面的位置\n    blockAnimateStart(node) {\n      const begin = this.countScrollRatio(node.offsetTop);\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight);\n      return {\n        begin,\n        end\n      };\n    },\n    // 计算当前位置距离顶部高度占整个页面的百分比\n    countScrollRatio(scrollTop) {\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4));\n    },\n    // 滚动事件处理\n    handleScroll() {\n      const top = document.documentElement.scrollTop;\n      const rate = this.countScrollRatio(top);\n      this.currentScrollRate = rate.toFixed(2);\n      this.activateAnimate(rate);\n    },\n    // 窗口大小改变事件处理\n    handleResize() {\n      this.updateScrollData();\n      this.countAnimateMomentInfo();\n    },\n    // 激活动画\n    activateAnimate(rate) {\n      let activeSection = '';\n      let activeSectionRate = 0;\n      for (let key in this.data.animateMomentInfo) {\n        const {\n          begin,\n          end\n        } = this.data.animateMomentInfo[key];\n        if (rate >= begin && rate <= end) {\n          const progress = (rate - begin) / (end - begin);\n          const clampedProgress = Math.min(1, Math.max(0, progress));\n          this.executeAnimate(key, clampedProgress);\n          activeSection = key;\n          activeSectionRate = (clampedProgress * 100).toFixed(1);\n        }\n      }\n      this.currentSection = activeSection;\n      this.currentSectionRate = activeSectionRate;\n    },\n    // 执行具体的动画\n    executeAnimate(id, rate) {\n      switch (id) {\n        case 'head':\n          this.headAnimate(rate);\n          break;\n        case 'intro':\n          this.introAnimate(rate);\n          break;\n        case 'tech':\n          this.techAnimate(rate);\n          break;\n        case 'company':\n          this.companyAnimate(rate);\n          break;\n        default:\n          console.log('no action for', id);\n      }\n    },\n    // 头部分区动画\n    headAnimate(rate) {\n      const title = this.$refs.headTitle;\n      const subtitle = this.$refs.headSubtitle;\n      const links = this.$refs.headLinks;\n      const image = this.$refs.headImage;\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * 50}px)`;\n        title.style.opacity = rate;\n      }\n      if (subtitle) {\n        subtitle.style.transform = `translateY(${(1 - rate) * 30}px)`;\n        subtitle.style.opacity = rate;\n      }\n      if (links) {\n        links.style.transform = `translateY(${(1 - rate) * 20}px)`;\n        links.style.opacity = rate;\n      }\n      if (image) {\n        image.style.transform = `scale(${0.8 + rate * 0.2}) translateX(${(1 - rate) * 100}px)`;\n        image.style.opacity = rate;\n      }\n    },\n    // 介绍分区动画\n    introAnimate(rate) {\n      const title = this.$refs.introTitle;\n      const text = this.$refs.introText;\n      const features = this.$refs.introFeatures;\n      if (title) {\n        title.style.transform = `translateX(${(1 - rate) * -100}px)`;\n        title.style.opacity = rate;\n      }\n      if (text) {\n        text.style.transform = `translateX(${(1 - rate) * -50}px)`;\n        title.style.opacity = rate;\n      }\n      if (features) {\n        features.style.transform = `translateY(${(1 - rate) * 50}px)`;\n        features.style.opacity = rate;\n      }\n    },\n    // 技术分区动画\n    techAnimate(rate) {\n      const title = this.$refs.techTitle;\n      const grid = this.$refs.techGrid;\n      if (title) {\n        title.style.transform = `scale(${0.5 + rate * 0.5})`;\n        title.style.opacity = rate;\n      }\n      if (grid) {\n        const items = grid.children;\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.2;\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)));\n          items[i].style.transform = `translateY(${(1 - itemRate) * 30}px) rotateX(${(1 - itemRate) * 15}deg)`;\n          items[i].style.opacity = itemRate;\n        }\n      }\n    },\n    // 公司分区动画\n    companyAnimate(rate) {\n      const title = this.$refs.companyTitle;\n      const products = this.$refs.companyProducts;\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * -30}px)`;\n        title.style.opacity = rate;\n      }\n      if (products) {\n        const items = products.children;\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.3;\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)));\n          items[i].style.transform = `scale(${0.8 + itemRate * 0.2}) rotateY(${(1 - itemRate) * 10}deg)`;\n          items[i].style.opacity = itemRate;\n        }\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "showDebug", "currentScrollRate", "currentSection", "currentSectionRate", "sections", "animateMomentInfo", "scrollHeight", "clientHeight", "mounted", "initScrollAnimation", "beforeUnmount", "window", "removeEventListener", "handleScroll", "handleResize", "methods", "updateScrollData", "countAnimateMomentInfo", "addEventListener", "document", "documentElement", "sectionId", "node", "getElementById", "blockAnimateStart", "begin", "countScrollRatio", "offsetTop", "end", "scrollTop", "Number", "toFixed", "top", "rate", "activateAnimate", "activeSection", "activeSectionRate", "key", "progress", "clampedProgress", "Math", "min", "max", "executeAnimate", "id", "headAnimate", "introAnimate", "techAnimate", "companyAnimate", "console", "log", "title", "$refs", "head<PERSON><PERSON>le", "subtitle", "headSubtitle", "links", "headLinks", "image", "headImage", "style", "transform", "opacity", "introTitle", "text", "introText", "features", "introFeatures", "techTitle", "grid", "techGrid", "items", "children", "i", "length", "delay", "itemRate", "companyTitle", "products", "companyProducts"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\AnimationDemo.vue"], "sourcesContent": ["<template>\n  <div class=\"animation-demo\">\n    <!-- 调试信息 -->\n    <div class=\"debug-info\" v-if=\"showDebug\">\n      <p>滚动进度: {{ currentScrollRate }}%</p>\n      <p>当前分区: {{ currentSection }}</p>\n      <p>分区进度: {{ currentSectionRate }}</p>\n    </div>\n\n    <!-- 头部分区 -->\n    <section id=\"head\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"headContent\">\n        <div class=\"hero-content\">\n          <h1 ref=\"headTitle\">iPhone 15 Pro</h1>\n          <h2 ref=\"headSubtitle\">钛金属。超强大。超Pro。</h2>\n          <div class=\"cta-links\" ref=\"headLinks\">\n            <a href=\"#\" class=\"link\">了解更多</a>\n            <a href=\"#\" class=\"link\">购买</a>\n          </div>\n        </div>\n        <div class=\"hero-image\" ref=\"headImage\">\n          <div class=\"phone-mockup\">📱</div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 介绍分区 -->\n    <section id=\"intro\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"introContent\">\n        <div class=\"intro-section\">\n          <h2 ref=\"introTitle\">创新科技</h2>\n          <p ref=\"introText\">探索Apple最新的技术突破，体验前所未有的性能与设计。</p>\n          <div class=\"feature-grid\" ref=\"introFeatures\">\n            <div class=\"feature-item\">\n              <div class=\"icon\">🚀</div>\n              <h3>A17 Pro芯片</h3>\n              <p>业界领先的3纳米工艺</p>\n            </div>\n            <div class=\"feature-item\">\n              <div class=\"icon\">💎</div>\n              <h3>钛金属设计</h3>\n              <p>轻盈坚固，精工打造</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 技术分区 -->\n    <section id=\"tech\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"techContent\">\n        <div class=\"tech-section\">\n          <h2 ref=\"techTitle\">技术规格</h2>\n          <div class=\"tech-grid\" ref=\"techGrid\">\n            <div class=\"tech-item\">\n              <div class=\"icon\">📺</div>\n              <h3>显示屏</h3>\n              <p>6.1英寸超视网膜XDR显示屏</p>\n            </div>\n            <div class=\"tech-item\">\n              <div class=\"icon\">📷</div>\n              <h3>摄像头</h3>\n              <p>4800万像素主摄像头系统</p>\n            </div>\n            <div class=\"tech-item\">\n              <div class=\"icon\">🔋</div>\n              <h3>电池</h3>\n              <p>长达29小时视频播放</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 公司分区 -->\n    <section id=\"company\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"companyContent\">\n        <div class=\"company-section\">\n          <h2 ref=\"companyTitle\">Apple生态</h2>\n          <div class=\"product-showcase\" ref=\"companyProducts\">\n            <div class=\"product-item\">\n              <div class=\"product-icon\">💻</div>\n              <h3>MacBook Air</h3>\n              <p>搭载 M2 芯片</p>\n            </div>\n            <div class=\"product-item\">\n              <div class=\"product-icon\">📱</div>\n              <h3>iPad Pro</h3>\n              <p>搭载 M2 芯片</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 切换调试模式按钮 -->\n    <button class=\"debug-toggle\" @click=\"showDebug = !showDebug\">\n      {{ showDebug ? '隐藏' : '显示' }}调试信息\n    </button>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AnimationDemo',\n  data() {\n    return {\n      showDebug: true,\n      currentScrollRate: 0,\n      currentSection: '',\n      currentSectionRate: 0,\n      data: {\n        sections: [],\n        animateMomentInfo: {},\n        scrollHeight: 0,\n        clientHeight: 0\n      }\n    }\n  },\n  mounted() {\n    this.initScrollAnimation()\n  },\n  beforeUnmount() {\n    window.removeEventListener('scroll', this.handleScroll)\n    window.removeEventListener('resize', this.handleResize)\n  },\n  methods: {\n    // 初始化滚动动画\n    initScrollAnimation() {\n      this.data.sections = ['head', 'intro', 'tech', 'company']\n      this.updateScrollData()\n      this.countAnimateMomentInfo()\n      \n      // 绑定滚动事件\n      window.addEventListener('scroll', this.handleScroll)\n      window.addEventListener('resize', this.handleResize)\n      \n      // 初始化动画状态\n      this.handleScroll()\n    },\n\n    // 更新滚动相关数据\n    updateScrollData() {\n      this.data.scrollHeight = document.documentElement.scrollHeight\n      this.data.clientHeight = document.documentElement.clientHeight\n    },\n\n    // 计算每个section的动画触发时机\n    countAnimateMomentInfo() {\n      for (const sectionId of this.data.sections) {\n        const node = document.getElementById(sectionId)\n        if (node) {\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node)\n        }\n      }\n    },\n\n    // 根据section的高度，计算它在页面的位置\n    blockAnimateStart(node) {\n      const begin = this.countScrollRatio(node.offsetTop)\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight)\n      return { begin, end }\n    },\n\n    // 计算当前位置距离顶部高度占整个页面的百分比\n    countScrollRatio(scrollTop) {\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4))\n    },\n\n    // 滚动事件处理\n    handleScroll() {\n      const top = document.documentElement.scrollTop\n      const rate = this.countScrollRatio(top)\n      this.currentScrollRate = rate.toFixed(2)\n      this.activateAnimate(rate)\n    },\n\n    // 窗口大小改变事件处理\n    handleResize() {\n      this.updateScrollData()\n      this.countAnimateMomentInfo()\n    },\n\n    // 激活动画\n    activateAnimate(rate) {\n      let activeSection = ''\n      let activeSectionRate = 0\n      \n      for (let key in this.data.animateMomentInfo) {\n        const { begin, end } = this.data.animateMomentInfo[key]\n        if (rate >= begin && rate <= end) {\n          const progress = ((rate - begin) / (end - begin))\n          const clampedProgress = Math.min(1, Math.max(0, progress))\n          this.executeAnimate(key, clampedProgress)\n          activeSection = key\n          activeSectionRate = (clampedProgress * 100).toFixed(1)\n        }\n      }\n      \n      this.currentSection = activeSection\n      this.currentSectionRate = activeSectionRate\n    },\n\n    // 执行具体的动画\n    executeAnimate(id, rate) {\n      switch (id) {\n        case 'head':\n          this.headAnimate(rate)\n          break\n        case 'intro':\n          this.introAnimate(rate)\n          break\n        case 'tech':\n          this.techAnimate(rate)\n          break\n        case 'company':\n          this.companyAnimate(rate)\n          break\n        default:\n          console.log('no action for', id)\n      }\n    },\n\n    // 头部分区动画\n    headAnimate(rate) {\n      const title = this.$refs.headTitle\n      const subtitle = this.$refs.headSubtitle\n      const links = this.$refs.headLinks\n      const image = this.$refs.headImage\n\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * 50}px)`\n        title.style.opacity = rate\n      }\n      if (subtitle) {\n        subtitle.style.transform = `translateY(${(1 - rate) * 30}px)`\n        subtitle.style.opacity = rate\n      }\n      if (links) {\n        links.style.transform = `translateY(${(1 - rate) * 20}px)`\n        links.style.opacity = rate\n      }\n      if (image) {\n        image.style.transform = `scale(${0.8 + rate * 0.2}) translateX(${(1 - rate) * 100}px)`\n        image.style.opacity = rate\n      }\n    },\n\n    // 介绍分区动画\n    introAnimate(rate) {\n      const title = this.$refs.introTitle\n      const text = this.$refs.introText\n      const features = this.$refs.introFeatures\n\n      if (title) {\n        title.style.transform = `translateX(${(1 - rate) * -100}px)`\n        title.style.opacity = rate\n      }\n      if (text) {\n        text.style.transform = `translateX(${(1 - rate) * -50}px)`\n        title.style.opacity = rate\n      }\n      if (features) {\n        features.style.transform = `translateY(${(1 - rate) * 50}px)`\n        features.style.opacity = rate\n      }\n    },\n\n    // 技术分区动画\n    techAnimate(rate) {\n      const title = this.$refs.techTitle\n      const grid = this.$refs.techGrid\n\n      if (title) {\n        title.style.transform = `scale(${0.5 + rate * 0.5})`\n        title.style.opacity = rate\n      }\n      if (grid) {\n        const items = grid.children\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.2\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))\n          items[i].style.transform = `translateY(${(1 - itemRate) * 30}px) rotateX(${(1 - itemRate) * 15}deg)`\n          items[i].style.opacity = itemRate\n        }\n      }\n    },\n\n    // 公司分区动画\n    companyAnimate(rate) {\n      const title = this.$refs.companyTitle\n      const products = this.$refs.companyProducts\n\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * -30}px)`\n        title.style.opacity = rate\n      }\n      if (products) {\n        const items = products.children\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.3\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))\n          items[i].style.transform = `scale(${0.8 + itemRate * 0.2}) rotateY(${(1 - itemRate) * 10}deg)`\n          items[i].style.opacity = itemRate\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.animation-demo {\n  position: relative;\n}\n\n/* 调试信息 */\n.debug-info {\n  position: fixed;\n  top: 60px;\n  right: 20px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  font-family: monospace;\n  font-size: 14px;\n  z-index: 1000;\n  backdrop-filter: blur(10px);\n}\n\n.debug-info p {\n  margin: 5px 0;\n}\n\n.debug-toggle {\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  background: #007aff;\n  color: white;\n  border: none;\n  padding: 12px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 14px;\n  z-index: 1000;\n  transition: all 0.3s ease;\n}\n\n.debug-toggle:hover {\n  background: #0056cc;\n  transform: scale(1.05);\n}\n\n/* 分区基础样式 */\n.titleBox {\n  height: 100vh;\n  position: relative;\n  overflow: hidden;\n}\n\n.sticky-content {\n  position: sticky;\n  top: 0;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 20px;\n  transition: all 0.1s ease-out;\n}\n\n/* 头部分区样式 */\n#head {\n  background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);\n  color: #fff;\n}\n\n#head .sticky-content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 60px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n#head .hero-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  text-align: left;\n}\n\n#head h1 {\n  font-size: 64px;\n  font-weight: 700;\n  margin-bottom: 20px;\n  line-height: 1.1;\n  opacity: 0;\n  transform: translateY(50px);\n}\n\n#head h2 {\n  font-size: 32px;\n  font-weight: 400;\n  margin-bottom: 40px;\n  color: #a1a1a6;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.cta-links {\n  display: flex;\n  gap: 30px;\n  opacity: 0;\n  transform: translateY(20px);\n}\n\n.link {\n  color: #2997ff;\n  text-decoration: none;\n  font-size: 21px;\n  font-weight: 500;\n  padding: 12px 24px;\n  border: 1px solid #2997ff;\n  border-radius: 25px;\n  transition: all 0.3s ease;\n}\n\n.link:hover {\n  background-color: #2997ff;\n  color: #fff;\n}\n\n.hero-image {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transform: scale(0.8) translateX(100px);\n}\n\n.phone-mockup {\n  font-size: 120px;\n  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.5));\n}\n\n/* 介绍分区样式 */\n#intro {\n  background: linear-gradient(135deg, #f5f5f7 0%, #e8e8ed 100%);\n  color: #1d1d1f;\n}\n\n.intro-section {\n  max-width: 800px;\n  text-align: center;\n}\n\n#intro h2 {\n  font-size: 56px;\n  font-weight: 700;\n  margin-bottom: 30px;\n  opacity: 0;\n  transform: translateX(-100px);\n}\n\n#intro p {\n  font-size: 24px;\n  line-height: 1.6;\n  margin-bottom: 50px;\n  color: #6e6e73;\n  opacity: 0;\n  transform: translateX(-50px);\n}\n\n.feature-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 40px;\n  opacity: 0;\n  transform: translateY(50px);\n}\n\n.feature-item {\n  background: #fff;\n  padding: 30px;\n  border-radius: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.feature-item .icon {\n  font-size: 48px;\n  margin-bottom: 20px;\n}\n\n.feature-item h3 {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 15px;\n  color: #1d1d1f;\n}\n\n.feature-item p {\n  font-size: 16px;\n  color: #6e6e73;\n  margin: 0;\n}\n\n/* 技术分区样式 */\n#tech {\n  background: linear-gradient(135deg, #1d1d1f 0%, #2d2d30 100%);\n  color: #fff;\n}\n\n.tech-section {\n  max-width: 1000px;\n  text-align: center;\n}\n\n#tech h2 {\n  font-size: 56px;\n  font-weight: 700;\n  margin-bottom: 60px;\n  opacity: 0;\n  transform: scale(0.5);\n}\n\n.tech-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 40px;\n}\n\n.tech-item {\n  background: rgba(255, 255, 255, 0.1);\n  padding: 40px 30px;\n  border-radius: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  opacity: 0;\n  transform: translateY(30px) rotateX(15deg);\n  perspective: 1000px;\n  text-align: center;\n}\n\n.tech-item .icon {\n  font-size: 48px;\n  margin-bottom: 20px;\n}\n\n.tech-item h3 {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 15px;\n  color: #fff;\n}\n\n.tech-item p {\n  font-size: 16px;\n  color: #a1a1a6;\n  margin: 0;\n}\n\n/* 公司分区样式 */\n#company {\n  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);\n  color: #fff;\n}\n\n.company-section {\n  max-width: 1000px;\n  text-align: center;\n}\n\n#company h2 {\n  font-size: 56px;\n  font-weight: 700;\n  margin-bottom: 60px;\n  opacity: 0;\n  transform: translateY(-30px);\n}\n\n.product-showcase {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 40px;\n}\n\n.product-item {\n  background: rgba(255, 255, 255, 0.15);\n  padding: 40px;\n  border-radius: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  opacity: 0;\n  transform: scale(0.8) rotateY(10deg);\n  perspective: 1000px;\n  text-align: center;\n}\n\n.product-item .product-icon {\n  font-size: 64px;\n  margin-bottom: 20px;\n}\n\n.product-item h3 {\n  font-size: 28px;\n  font-weight: 600;\n  margin-bottom: 15px;\n  color: #fff;\n}\n\n.product-item p {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.8);\n  margin: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  #head .sticky-content {\n    grid-template-columns: 1fr;\n    text-align: center;\n  }\n\n  .tech-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .product-showcase {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  #head h1 {\n    font-size: 48px;\n  }\n\n  #head h2 {\n    font-size: 24px;\n  }\n\n  #intro h2, #tech h2, #company h2 {\n    font-size: 40px;\n  }\n\n  .feature-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .cta-links {\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .debug-info {\n    top: 10px;\n    right: 10px;\n    font-size: 12px;\n    padding: 10px;\n  }\n}\n</style>\n"], "mappings": "AAuGA,eAAe;EACbA,IAAI,EAAE,eAAe;EACrBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,IAAI;MACfC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,EAAE;MAClBC,kBAAkB,EAAE,CAAC;MACrBJ,IAAI,EAAE;QACJK,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,CAAC,CAAC;QACrBC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;MAChB;IACF;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,mBAAmB,CAAC;EAC3B,CAAC;EACDC,aAAaA,CAAA,EAAG;IACdC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY;IACtDF,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACE,YAAY;EACxD,CAAC;EACDC,OAAO,EAAE;IACP;IACAN,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACV,IAAI,CAACK,QAAO,GAAI,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS;MACxD,IAAI,CAACY,gBAAgB,CAAC;MACtB,IAAI,CAACC,sBAAsB,CAAC;;MAE5B;MACAN,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACL,YAAY;MACnDF,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACJ,YAAY;;MAEnD;MACA,IAAI,CAACD,YAAY,CAAC;IACpB,CAAC;IAED;IACAG,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACjB,IAAI,CAACO,YAAW,GAAIa,QAAQ,CAACC,eAAe,CAACd,YAAW;MAC7D,IAAI,CAACP,IAAI,CAACQ,YAAW,GAAIY,QAAQ,CAACC,eAAe,CAACb,YAAW;IAC/D,CAAC;IAED;IACAU,sBAAsBA,CAAA,EAAG;MACvB,KAAK,MAAMI,SAAQ,IAAK,IAAI,CAACtB,IAAI,CAACK,QAAQ,EAAE;QAC1C,MAAMkB,IAAG,GAAIH,QAAQ,CAACI,cAAc,CAACF,SAAS;QAC9C,IAAIC,IAAI,EAAE;UACR,IAAI,CAACvB,IAAI,CAACM,iBAAiB,CAACgB,SAAS,IAAI,IAAI,CAACG,iBAAiB,CAACF,IAAI;QACtE;MACF;IACF,CAAC;IAED;IACAE,iBAAiBA,CAACF,IAAI,EAAE;MACtB,MAAMG,KAAI,GAAI,IAAI,CAACC,gBAAgB,CAACJ,IAAI,CAACK,SAAS;MAClD,MAAMC,GAAE,GAAI,IAAI,CAACF,gBAAgB,CAACJ,IAAI,CAACK,SAAQ,GAAIL,IAAI,CAACf,YAAY;MACpE,OAAO;QAAEkB,KAAK;QAAEG;MAAI;IACtB,CAAC;IAED;IACAF,gBAAgBA,CAACG,SAAS,EAAE;MAC1B,OAAOC,MAAM,CAAC,CAAC,GAAE,GAAID,SAAQ,IAAK,IAAI,CAAC9B,IAAI,CAACO,YAAW,GAAI,IAAI,CAACP,IAAI,CAACQ,YAAY,CAAC,EAAEwB,OAAO,CAAC,CAAC,CAAC;IAChG,CAAC;IAED;IACAlB,YAAYA,CAAA,EAAG;MACb,MAAMmB,GAAE,GAAIb,QAAQ,CAACC,eAAe,CAACS,SAAQ;MAC7C,MAAMI,IAAG,GAAI,IAAI,CAACP,gBAAgB,CAACM,GAAG;MACtC,IAAI,CAAC/B,iBAAgB,GAAIgC,IAAI,CAACF,OAAO,CAAC,CAAC;MACvC,IAAI,CAACG,eAAe,CAACD,IAAI;IAC3B,CAAC;IAED;IACAnB,YAAYA,CAAA,EAAG;MACb,IAAI,CAACE,gBAAgB,CAAC;MACtB,IAAI,CAACC,sBAAsB,CAAC;IAC9B,CAAC;IAED;IACAiB,eAAeA,CAACD,IAAI,EAAE;MACpB,IAAIE,aAAY,GAAI,EAAC;MACrB,IAAIC,iBAAgB,GAAI;MAExB,KAAK,IAAIC,GAAE,IAAK,IAAI,CAACtC,IAAI,CAACM,iBAAiB,EAAE;QAC3C,MAAM;UAAEoB,KAAK;UAAEG;QAAI,IAAI,IAAI,CAAC7B,IAAI,CAACM,iBAAiB,CAACgC,GAAG;QACtD,IAAIJ,IAAG,IAAKR,KAAI,IAAKQ,IAAG,IAAKL,GAAG,EAAE;UAChC,MAAMU,QAAO,GAAK,CAACL,IAAG,GAAIR,KAAK,KAAKG,GAAE,GAAIH,KAAK,CAAC;UAChD,MAAMc,eAAc,GAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,QAAQ,CAAC;UACzD,IAAI,CAACK,cAAc,CAACN,GAAG,EAAEE,eAAe;UACxCJ,aAAY,GAAIE,GAAE;UAClBD,iBAAgB,GAAI,CAACG,eAAc,GAAI,GAAG,EAAER,OAAO,CAAC,CAAC;QACvD;MACF;MAEA,IAAI,CAAC7B,cAAa,GAAIiC,aAAY;MAClC,IAAI,CAAChC,kBAAiB,GAAIiC,iBAAgB;IAC5C,CAAC;IAED;IACAO,cAAcA,CAACC,EAAE,EAAEX,IAAI,EAAE;MACvB,QAAQW,EAAE;QACR,KAAK,MAAM;UACT,IAAI,CAACC,WAAW,CAACZ,IAAI;UACrB;QACF,KAAK,OAAO;UACV,IAAI,CAACa,YAAY,CAACb,IAAI;UACtB;QACF,KAAK,MAAM;UACT,IAAI,CAACc,WAAW,CAACd,IAAI;UACrB;QACF,KAAK,SAAS;UACZ,IAAI,CAACe,cAAc,CAACf,IAAI;UACxB;QACF;UACEgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEN,EAAE;MACnC;IACF,CAAC;IAED;IACAC,WAAWA,CAACZ,IAAI,EAAE;MAChB,MAAMkB,KAAI,GAAI,IAAI,CAACC,KAAK,CAACC,SAAQ;MACjC,MAAMC,QAAO,GAAI,IAAI,CAACF,KAAK,CAACG,YAAW;MACvC,MAAMC,KAAI,GAAI,IAAI,CAACJ,KAAK,CAACK,SAAQ;MACjC,MAAMC,KAAI,GAAI,IAAI,CAACN,KAAK,CAACO,SAAQ;MAEjC,IAAIR,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAI5B,IAAI,IAAI,EAAE,KAAI;QACzDkB,KAAK,CAACS,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC3B;MACA,IAAIqB,QAAQ,EAAE;QACZA,QAAQ,CAACM,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAI5B,IAAI,IAAI,EAAE,KAAI;QAC5DqB,QAAQ,CAACM,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC9B;MACA,IAAIuB,KAAK,EAAE;QACTA,KAAK,CAACI,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAI5B,IAAI,IAAI,EAAE,KAAI;QACzDuB,KAAK,CAACI,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC3B;MACA,IAAIyB,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,CAACC,SAAQ,GAAI,SAAS,GAAE,GAAI5B,IAAG,GAAI,GAAG,gBAAgB,CAAC,IAAIA,IAAI,IAAI,GAAG,KAAI;QACrFyB,KAAK,CAACE,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC3B;IACF,CAAC;IAED;IACAa,YAAYA,CAACb,IAAI,EAAE;MACjB,MAAMkB,KAAI,GAAI,IAAI,CAACC,KAAK,CAACW,UAAS;MAClC,MAAMC,IAAG,GAAI,IAAI,CAACZ,KAAK,CAACa,SAAQ;MAChC,MAAMC,QAAO,GAAI,IAAI,CAACd,KAAK,CAACe,aAAY;MAExC,IAAIhB,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAI5B,IAAI,IAAI,CAAC,GAAG,KAAI;QAC3DkB,KAAK,CAACS,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC3B;MACA,IAAI+B,IAAI,EAAE;QACRA,IAAI,CAACJ,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAI5B,IAAI,IAAI,CAAC,EAAE,KAAI;QACzDkB,KAAK,CAACS,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC3B;MACA,IAAIiC,QAAQ,EAAE;QACZA,QAAQ,CAACN,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAI5B,IAAI,IAAI,EAAE,KAAI;QAC5DiC,QAAQ,CAACN,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC9B;IACF,CAAC;IAED;IACAc,WAAWA,CAACd,IAAI,EAAE;MAChB,MAAMkB,KAAI,GAAI,IAAI,CAACC,KAAK,CAACgB,SAAQ;MACjC,MAAMC,IAAG,GAAI,IAAI,CAACjB,KAAK,CAACkB,QAAO;MAE/B,IAAInB,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,SAAS,GAAE,GAAI5B,IAAG,GAAI,GAAG,GAAE;QACnDkB,KAAK,CAACS,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC3B;MACA,IAAIoC,IAAI,EAAE;QACR,MAAME,KAAI,GAAIF,IAAI,CAACG,QAAO;QAC1B,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACrC,MAAME,KAAI,GAAIF,CAAA,GAAI,GAAE;UACpB,MAAMG,QAAO,GAAIpC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACR,IAAG,GAAI0C,KAAK,KAAK,IAAIA,KAAK,CAAC,CAAC;UACtEJ,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIe,QAAQ,IAAI,EAAE,eAAe,CAAC,IAAIA,QAAQ,IAAI,EAAE,MAAK;UACnGL,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACE,OAAM,GAAIc,QAAO;QAClC;MACF;IACF,CAAC;IAED;IACA5B,cAAcA,CAACf,IAAI,EAAE;MACnB,MAAMkB,KAAI,GAAI,IAAI,CAACC,KAAK,CAACyB,YAAW;MACpC,MAAMC,QAAO,GAAI,IAAI,CAAC1B,KAAK,CAAC2B,eAAc;MAE1C,IAAI5B,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAI5B,IAAI,IAAI,CAAC,EAAE,KAAI;QAC1DkB,KAAK,CAACS,KAAK,CAACE,OAAM,GAAI7B,IAAG;MAC3B;MACA,IAAI6C,QAAQ,EAAE;QACZ,MAAMP,KAAI,GAAIO,QAAQ,CAACN,QAAO;QAC9B,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACrC,MAAME,KAAI,GAAIF,CAAA,GAAI,GAAE;UACpB,MAAMG,QAAO,GAAIpC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACR,IAAG,GAAI0C,KAAK,KAAK,IAAIA,KAAK,CAAC,CAAC;UACtEJ,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACC,SAAQ,GAAI,SAAS,GAAE,GAAIe,QAAO,GAAI,GAAG,aAAa,CAAC,IAAIA,QAAQ,IAAI,EAAE,MAAK;UAC7FL,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACE,OAAM,GAAIc,QAAO;QAClC;MACF;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}