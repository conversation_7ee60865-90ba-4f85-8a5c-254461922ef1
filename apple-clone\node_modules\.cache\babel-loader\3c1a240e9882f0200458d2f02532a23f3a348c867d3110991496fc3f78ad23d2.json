{"ast": null, "code": "import { mapState, mapMutations } from 'vuex';\nexport default {\n  name: 'Search',\n  data() {\n    return {\n      searchQuery: ''\n    };\n  },\n  computed: {\n    ...mapState(['isSearchOpen'])\n  },\n  methods: {\n    ...mapMutations(['setSearchQuery', 'toggleSearch']),\n    handleSearch() {\n      this.setSearchQuery(this.searchQuery);\n    },\n    closeSearch() {\n      this.toggleSearch();\n      this.searchQuery = '';\n      this.setSearchQuery('');\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapMutations", "name", "data", "searchQuery", "computed", "methods", "handleSearch", "setSearch<PERSON>uery", "closeSearch", "toggleSearch"], "sources": ["E:\\cursor\\apple-clone\\src\\components\\Search\\Search.vue"], "sourcesContent": ["<template>\r\n  <div class=\"search-overlay\" :class=\"{ active: isSearchOpen }\">\r\n    <div class=\"search-container\">\r\n      <div class=\"search-header\">\r\n        <div class=\"search-input-wrapper\">\r\n          <svg class=\"search-icon\" xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"44\" viewBox=\"0 0 15 44\">\r\n            <path d=\"M14.298,27.202l-3.87-3.87c0.701-0.929,1.122-2.081,1.122-3.332c0-3.06-2.489-5.55-5.55-5.55c-3.06,0-5.55,2.49-5.55,5.55 c0,3.061,2.49,5.55,5.55,5.55c1.251,0,2.403-0.421,3.332-1.122l3.87,3.87c0.151,0.151,0.35,0.228,0.548,0.228 s0.396-0.076,0.548-0.228C14.601,27.995,14.601,27.505,14.298,27.202z M1.55,20c0-2.454,1.997-4.45,4.45-4.45 c2.454,0,4.45,1.997,4.45,4.45S8.454,24.45,6,24.45C3.546,24.45,1.55,22.454,1.55,20z\" fill=\"#86868b\"/>\r\n          </svg>\r\n          <input\r\n            type=\"text\"\r\n            v-model=\"searchQuery\"\r\n            placeholder=\"搜索 apple.com.cn\"\r\n            @input=\"handleSearch\"\r\n          />\r\n        </div>\r\n        <button class=\"close-button\" @click=\"closeSearch\">取消</button>\r\n      </div>\r\n      \r\n      <div class=\"search-results\" v-if=\"searchQuery\">\r\n        <div class=\"quick-links\">\r\n          <h3>快速链接</h3>\r\n          <div class=\"links-grid\">\r\n            <router-link to=\"/store\" class=\"quick-link\">访问 Apple Store 在线商店</router-link>\r\n            <router-link to=\"/accessories\" class=\"quick-link\">配件</router-link>\r\n            <router-link to=\"/airpods\" class=\"quick-link\">AirPods</router-link>\r\n            <router-link to=\"/airtag\" class=\"quick-link\">AirTag</router-link>\r\n            <router-link to=\"/apple-tv\" class=\"quick-link\">Apple TV</router-link>\r\n            <router-link to=\"/ipad\" class=\"quick-link\">iPad</router-link>\r\n            <router-link to=\"/iphone\" class=\"quick-link\">iPhone</router-link>\r\n            <router-link to=\"/mac\" class=\"quick-link\">Mac</router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapMutations } from 'vuex'\r\n\r\nexport default {\r\n  name: 'Search',\r\n  data() {\r\n    return {\r\n      searchQuery: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['isSearchOpen'])\r\n  },\r\n  methods: {\r\n    ...mapMutations(['setSearchQuery', 'toggleSearch']),\r\n    handleSearch() {\r\n      this.setSearchQuery(this.searchQuery)\r\n    },\r\n    closeSearch() {\r\n      this.toggleSearch()\r\n      this.searchQuery = ''\r\n      this.setSearchQuery('')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.search-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: saturate(180%) blur(20px);\r\n  z-index: 10000;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-overlay.active {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n.search-container {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.search-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.search-input-wrapper {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 10px 15px;\r\n}\r\n\r\n.search-icon {\r\n  margin-right: 10px;\r\n}\r\n\r\ninput {\r\n  width: 100%;\r\n  background: none;\r\n  border: none;\r\n  color: #fff;\r\n  font-size: 17px;\r\n  outline: none;\r\n}\r\n\r\ninput::placeholder {\r\n  color: #86868b;\r\n}\r\n\r\n.close-button {\r\n  background: none;\r\n  border: none;\r\n  color: #fff;\r\n  font-size: 17px;\r\n  cursor: pointer;\r\n  padding: 10px;\r\n}\r\n\r\n.search-results {\r\n  color: #fff;\r\n}\r\n\r\n.quick-links h3 {\r\n  font-size: 12px;\r\n  color: #86868b;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.links-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.quick-link {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.quick-link:hover {\r\n  text-decoration: underline;\r\n}\r\n</style> "], "mappings": "AAsCA,SAASA,QAAQ,EAAEC,YAAW,QAAS,MAAK;AAE5C,eAAe;EACbC,IAAI,EAAE,QAAQ;EACdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,GAAGL,QAAQ,CAAC,CAAC,cAAc,CAAC;EAC9B,CAAC;EACDM,OAAO,EAAE;IACP,GAAGL,YAAY,CAAC,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACnDM,YAAYA,CAAA,EAAG;MACb,IAAI,CAACC,cAAc,CAAC,IAAI,CAACJ,WAAW;IACtC,CAAC;IACDK,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACC,YAAY,CAAC;MAClB,IAAI,CAACN,WAAU,GAAI,EAAC;MACpB,IAAI,CAACI,cAAc,CAAC,EAAE;IACxB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}