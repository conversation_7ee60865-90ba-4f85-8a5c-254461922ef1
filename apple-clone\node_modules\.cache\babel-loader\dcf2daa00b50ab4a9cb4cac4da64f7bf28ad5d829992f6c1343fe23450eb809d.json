{"ast": null, "code": "export default {\n  name: 'Home',\n  data() {\n    return {\n      data: {\n        sections: [],\n        animateMomentInfo: {},\n        scrollHeight: 0,\n        clientHeight: 0\n      }\n    };\n  },\n  mounted() {\n    this.initScrollAnimation();\n  },\n  beforeUnmount() {\n    window.removeEventListener('scroll', this.handleScroll);\n    window.removeEventListener('resize', this.handleResize);\n  },\n  methods: {\n    // 初始化滚动动画\n    initScrollAnimation() {\n      this.data.sections = ['head', 'intro', 'tech', 'company'];\n      this.updateScrollData();\n      this.countAnimateMomentInfo();\n\n      // 绑定滚动事件\n      window.addEventListener('scroll', this.handleScroll);\n      window.addEventListener('resize', this.handleResize);\n\n      // 初始化动画状态\n      this.handleScroll();\n    },\n    // 更新滚动相关数据\n    updateScrollData() {\n      this.data.scrollHeight = document.documentElement.scrollHeight;\n      this.data.clientHeight = document.documentElement.clientHeight;\n    },\n    // 计算每个section的动画触发时机\n    countAnimateMomentInfo() {\n      for (const sectionId of this.data.sections) {\n        const node = document.getElementById(sectionId);\n        if (node) {\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node);\n        }\n      }\n    },\n    // 根据section的高度，计算它在页面的位置\n    blockAnimateStart(node) {\n      const begin = this.countScrollRatio(node.offsetTop);\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight);\n      return {\n        begin,\n        end\n      };\n    },\n    // 计算当前位置距离顶部高度占整个页面的百分比\n    countScrollRatio(scrollTop) {\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4));\n    },\n    // 滚动事件处理\n    handleScroll() {\n      const top = document.documentElement.scrollTop;\n      this.activateAnimate(this.countScrollRatio(top));\n    },\n    // 窗口大小改变事件处理\n    handleResize() {\n      this.updateScrollData();\n      this.countAnimateMomentInfo();\n    },\n    // 激活动画\n    activateAnimate(rate) {\n      for (let key in this.data.animateMomentInfo) {\n        const {\n          begin,\n          end\n        } = this.data.animateMomentInfo[key];\n        if (rate >= begin && rate <= end) {\n          const progress = ((rate - begin) / (end - begin)).toFixed(3);\n          this.executeAnimate(key, Math.min(1, Math.max(0, progress)));\n        }\n      }\n    },\n    // 执行具体的动画\n    executeAnimate(id, rate) {\n      switch (id) {\n        case 'head':\n          this.headAnimate(rate);\n          break;\n        case 'intro':\n          this.introAnimate(rate);\n          break;\n        case 'tech':\n          this.techAnimate(rate);\n          break;\n        case 'company':\n          this.companyAnimate(rate);\n          break;\n        default:\n          console.log('no action for', id);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "sections", "animateMomentInfo", "scrollHeight", "clientHeight", "mounted", "initScrollAnimation", "beforeUnmount", "window", "removeEventListener", "handleScroll", "handleResize", "methods", "updateScrollData", "countAnimateMomentInfo", "addEventListener", "document", "documentElement", "sectionId", "node", "getElementById", "blockAnimateStart", "begin", "countScrollRatio", "offsetTop", "end", "scrollTop", "Number", "toFixed", "top", "activateAnimate", "rate", "key", "progress", "executeAnimate", "Math", "min", "max", "id", "headAnimate", "introAnimate", "techAnimate", "companyAnimate", "console", "log"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <!-- 头部分区 -->\r\n    <section id=\"head\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"headContent\">\r\n        <div class=\"hero-content\">\r\n          <h1 ref=\"headTitle\">iPhone 15 Pro</h1>\r\n          <h2 ref=\"headSubtitle\">钛金属。超强大。超Pro。</h2>\r\n          <div class=\"cta-links\" ref=\"headLinks\">\r\n            <router-link to=\"/iphone-15-pro\" class=\"link\">了解更多</router-link>\r\n            <router-link to=\"/shop/iphone-15-pro\" class=\"link\">购买</router-link>\r\n          </div>\r\n        </div>\r\n        <div class=\"hero-image\" ref=\"headImage\">\r\n          <img src=\"https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone+15+Pro\" alt=\"iPhone 15 Pro\" />\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 介绍分区 -->\r\n    <section id=\"intro\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"introContent\">\r\n        <div class=\"intro-section\">\r\n          <h2 ref=\"introTitle\">创新科技</h2>\r\n          <p ref=\"introText\">探索Apple最新的技术突破，体验前所未有的性能与设计。</p>\r\n          <div class=\"feature-grid\" ref=\"introFeatures\">\r\n            <div class=\"feature-item\">\r\n              <h3>A17 Pro芯片</h3>\r\n              <p>业界领先的3纳米工艺</p>\r\n            </div>\r\n            <div class=\"feature-item\">\r\n              <h3>钛金属设计</h3>\r\n              <p>轻盈坚固，精工打造</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 技术分区 -->\r\n    <section id=\"tech\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"techContent\">\r\n        <div class=\"tech-section\">\r\n          <h2 ref=\"techTitle\">技术规格</h2>\r\n          <div class=\"tech-grid\" ref=\"techGrid\">\r\n            <div class=\"tech-item\">\r\n              <h3>显示屏</h3>\r\n              <p>6.1英寸超视网膜XDR显示屏</p>\r\n            </div>\r\n            <div class=\"tech-item\">\r\n              <h3>摄像头</h3>\r\n              <p>4800万像素主摄像头系统</p>\r\n            </div>\r\n            <div class=\"tech-item\">\r\n              <h3>电池</h3>\r\n              <p>长达29小时视频播放</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 公司分区 -->\r\n    <section id=\"company\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"companyContent\">\r\n        <div class=\"company-section\">\r\n          <h2 ref=\"companyTitle\">Apple生态</h2>\r\n          <div class=\"product-showcase\" ref=\"companyProducts\">\r\n            <div class=\"product-item\">\r\n              <h3>MacBook Air</h3>\r\n              <p>搭载 M2 芯片</p>\r\n              <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=MacBook+Air\" alt=\"MacBook Air\" />\r\n            </div>\r\n            <div class=\"product-item\">\r\n              <h3>iPad Pro</h3>\r\n              <p>搭载 M2 芯片</p>\r\n              <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Pro\" alt=\"iPad Pro\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      data: {\r\n        sections: [],\r\n        animateMomentInfo: {},\r\n        scrollHeight: 0,\r\n        clientHeight: 0\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initScrollAnimation()\r\n  },\r\n  beforeUnmount() {\r\n    window.removeEventListener('scroll', this.handleScroll)\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  methods: {\r\n    // 初始化滚动动画\r\n    initScrollAnimation() {\r\n      this.data.sections = ['head', 'intro', 'tech', 'company']\r\n      this.updateScrollData()\r\n      this.countAnimateMomentInfo()\r\n\r\n      // 绑定滚动事件\r\n      window.addEventListener('scroll', this.handleScroll)\r\n      window.addEventListener('resize', this.handleResize)\r\n\r\n      // 初始化动画状态\r\n      this.handleScroll()\r\n    },\r\n\r\n    // 更新滚动相关数据\r\n    updateScrollData() {\r\n      this.data.scrollHeight = document.documentElement.scrollHeight\r\n      this.data.clientHeight = document.documentElement.clientHeight\r\n    },\r\n\r\n    // 计算每个section的动画触发时机\r\n    countAnimateMomentInfo() {\r\n      for (const sectionId of this.data.sections) {\r\n        const node = document.getElementById(sectionId)\r\n        if (node) {\r\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 根据section的高度，计算它在页面的位置\r\n    blockAnimateStart(node) {\r\n      const begin = this.countScrollRatio(node.offsetTop)\r\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight)\r\n      return { begin, end }\r\n    },\r\n\r\n    // 计算当前位置距离顶部高度占整个页面的百分比\r\n    countScrollRatio(scrollTop) {\r\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4))\r\n    },\r\n\r\n    // 滚动事件处理\r\n    handleScroll() {\r\n      const top = document.documentElement.scrollTop\r\n      this.activateAnimate(this.countScrollRatio(top))\r\n    },\r\n\r\n    // 窗口大小改变事件处理\r\n    handleResize() {\r\n      this.updateScrollData()\r\n      this.countAnimateMomentInfo()\r\n    },\r\n\r\n    // 激活动画\r\n    activateAnimate(rate) {\r\n      for (let key in this.data.animateMomentInfo) {\r\n        const { begin, end } = this.data.animateMomentInfo[key]\r\n        if (rate >= begin && rate <= end) {\r\n          const progress = ((rate - begin) / (end - begin)).toFixed(3)\r\n          this.executeAnimate(key, Math.min(1, Math.max(0, progress)))\r\n        }\r\n      }\r\n    },\r\n\r\n    // 执行具体的动画\r\n    executeAnimate(id, rate) {\r\n      switch (id) {\r\n        case 'head':\r\n          this.headAnimate(rate)\r\n          break\r\n        case 'intro':\r\n          this.introAnimate(rate)\r\n          break\r\n        case 'tech':\r\n          this.techAnimate(rate)\r\n          break\r\n        case 'company':\r\n          this.companyAnimate(rate)\r\n          break\r\n        default:\r\n          console.log('no action for', id)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding-top: 44px;\r\n}\r\n\r\n.hero {\r\n  background-color: #000;\r\n  color: #fff;\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n}\r\n\r\n.hero-content {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.hero h1 {\r\n  font-size: 56px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.hero h2 {\r\n  font-size: 28px;\r\n  font-weight: 400;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.cta-links {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 35px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.link {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 21px;\r\n}\r\n\r\n.link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.hero-image img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n.product-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  padding: 20px;\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.product-item {\r\n  background-color: #fbfbfd;\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  border-radius: 18px;\r\n}\r\n\r\n.product-item h3 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.product-item p {\r\n  font-size: 21px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.product-item img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .product-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .hero h1 {\r\n    font-size: 40px;\r\n  }\r\n\r\n  .hero h2 {\r\n    font-size: 24px;\r\n  }\r\n}\r\n</style> "], "mappings": "AAsFA,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLA,IAAI,EAAE;QACJC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,CAAC,CAAC;QACrBC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;MAChB;IACF;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,mBAAmB,CAAC;EAC3B,CAAC;EACDC,aAAaA,CAAA,EAAG;IACdC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY;IACtDF,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACE,YAAY;EACxD,CAAC;EACDC,OAAO,EAAE;IACP;IACAN,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACN,IAAI,CAACC,QAAO,GAAI,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS;MACxD,IAAI,CAACY,gBAAgB,CAAC;MACtB,IAAI,CAACC,sBAAsB,CAAC;;MAE5B;MACAN,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACL,YAAY;MACnDF,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACJ,YAAY;;MAEnD;MACA,IAAI,CAACD,YAAY,CAAC;IACpB,CAAC;IAED;IACAG,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACb,IAAI,CAACG,YAAW,GAAIa,QAAQ,CAACC,eAAe,CAACd,YAAW;MAC7D,IAAI,CAACH,IAAI,CAACI,YAAW,GAAIY,QAAQ,CAACC,eAAe,CAACb,YAAW;IAC/D,CAAC;IAED;IACAU,sBAAsBA,CAAA,EAAG;MACvB,KAAK,MAAMI,SAAQ,IAAK,IAAI,CAAClB,IAAI,CAACC,QAAQ,EAAE;QAC1C,MAAMkB,IAAG,GAAIH,QAAQ,CAACI,cAAc,CAACF,SAAS;QAC9C,IAAIC,IAAI,EAAE;UACR,IAAI,CAACnB,IAAI,CAACE,iBAAiB,CAACgB,SAAS,IAAI,IAAI,CAACG,iBAAiB,CAACF,IAAI;QACtE;MACF;IACF,CAAC;IAED;IACAE,iBAAiBA,CAACF,IAAI,EAAE;MACtB,MAAMG,KAAI,GAAI,IAAI,CAACC,gBAAgB,CAACJ,IAAI,CAACK,SAAS;MAClD,MAAMC,GAAE,GAAI,IAAI,CAACF,gBAAgB,CAACJ,IAAI,CAACK,SAAQ,GAAIL,IAAI,CAACf,YAAY;MACpE,OAAO;QAAEkB,KAAK;QAAEG;MAAI;IACtB,CAAC;IAED;IACAF,gBAAgBA,CAACG,SAAS,EAAE;MAC1B,OAAOC,MAAM,CAAC,CAAC,GAAE,GAAID,SAAQ,IAAK,IAAI,CAAC1B,IAAI,CAACG,YAAW,GAAI,IAAI,CAACH,IAAI,CAACI,YAAY,CAAC,EAAEwB,OAAO,CAAC,CAAC,CAAC;IAChG,CAAC;IAED;IACAlB,YAAYA,CAAA,EAAG;MACb,MAAMmB,GAAE,GAAIb,QAAQ,CAACC,eAAe,CAACS,SAAQ;MAC7C,IAAI,CAACI,eAAe,CAAC,IAAI,CAACP,gBAAgB,CAACM,GAAG,CAAC;IACjD,CAAC;IAED;IACAlB,YAAYA,CAAA,EAAG;MACb,IAAI,CAACE,gBAAgB,CAAC;MACtB,IAAI,CAACC,sBAAsB,CAAC;IAC9B,CAAC;IAED;IACAgB,eAAeA,CAACC,IAAI,EAAE;MACpB,KAAK,IAAIC,GAAE,IAAK,IAAI,CAAChC,IAAI,CAACE,iBAAiB,EAAE;QAC3C,MAAM;UAAEoB,KAAK;UAAEG;QAAI,IAAI,IAAI,CAACzB,IAAI,CAACE,iBAAiB,CAAC8B,GAAG;QACtD,IAAID,IAAG,IAAKT,KAAI,IAAKS,IAAG,IAAKN,GAAG,EAAE;UAChC,MAAMQ,QAAO,GAAI,CAAC,CAACF,IAAG,GAAIT,KAAK,KAAKG,GAAE,GAAIH,KAAK,CAAC,EAAEM,OAAO,CAAC,CAAC;UAC3D,IAAI,CAACM,cAAc,CAACF,GAAG,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,QAAQ,CAAC,CAAC;QAC7D;MACF;IACF,CAAC;IAED;IACAC,cAAcA,CAACI,EAAE,EAAEP,IAAI,EAAE;MACvB,QAAQO,EAAE;QACR,KAAK,MAAM;UACT,IAAI,CAACC,WAAW,CAACR,IAAI;UACrB;QACF,KAAK,OAAO;UACV,IAAI,CAACS,YAAY,CAACT,IAAI;UACtB;QACF,KAAK,MAAM;UACT,IAAI,CAACU,WAAW,CAACV,IAAI;UACrB;QACF,KAAK,SAAS;UACZ,IAAI,CAACW,cAAc,CAACX,IAAI;UACxB;QACF;UACEY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEN,EAAE;MACnC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}