{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\ncreateApp(App).use(store).use(router).mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "store", "use", "mount"], "sources": ["E:/cursor/apple-clone/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\n\r\ncreateApp(App)\r\n  .use(store)\r\n  .use(router)\r\n  .mount('#app') "], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAE3BH,SAAS,CAACC,GAAG,CAAC,CACXG,GAAG,CAACD,KAAK,CAAC,CACVC,GAAG,CAACF,MAAM,CAAC,CACXG,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}