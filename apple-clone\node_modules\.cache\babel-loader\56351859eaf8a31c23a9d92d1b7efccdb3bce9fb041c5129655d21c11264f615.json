{"ast": null, "code": "export default {\n  name: 'iPhone'\n};", "map": {"version": 3, "names": ["name"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\iPhone.vue"], "sourcesContent": ["<template>\n  <div class=\"iphone\">\n    <section class=\"hero\">\n      <div class=\"hero-content\">\n        <h1>iPhone</h1>\n        <h2>强大。美丽。持久。</h2>\n        <div class=\"cta-links\">\n          <router-link to=\"/iphone/iphone-15\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/iphone\" class=\"link\">购买</router-link>\n        </div>\n      </div>\n      <div class=\"hero-image\">\n        <img src=\"https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone\" alt=\"iPhone\" />\n      </div>\n    </section>\n\n    <section class=\"product-grid\">\n      <div class=\"product-item\">\n        <h3>iPhone 15 Pro</h3>\n        <p>钛金属。超强大。超Pro。</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/iphone-15-pro\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/iphone-15-pro\" class=\"link\">购买</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/000000/FFFFFF?text=iPhone+15+Pro\" alt=\"iPhone 15 Pro\" />\n      </div>\n\n      <div class=\"product-item\">\n        <h3>iPhone 15</h3>\n        <p>新一代iPhone</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/iphone-15\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/iphone-15\" class=\"link\">购买</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPhone+15\" alt=\"iPhone 15\" />\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'iPhone'\n}\n</script>\n\n<style scoped>\n.iphone {\n  padding-top: 44px;\n}\n\n.hero {\n  background-color: #000;\n  color: #fff;\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.hero-content {\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.hero h1 {\n  font-size: 56px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.hero h2 {\n  font-size: 28px;\n  font-weight: 400;\n  margin-bottom: 20px;\n}\n\n.cta-links {\n  display: flex;\n  justify-content: center;\n  gap: 35px;\n  margin-bottom: 20px;\n}\n\n.link {\n  color: #2997ff;\n  text-decoration: none;\n  font-size: 21px;\n}\n\n.link:hover {\n  text-decoration: underline;\n}\n\n.hero-image img {\n  max-width: 100%;\n  height: auto;\n}\n\n.product-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20px;\n  padding: 20px;\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.product-item {\n  background-color: #fbfbfd;\n  text-align: center;\n  padding: 40px 20px;\n  border-radius: 18px;\n}\n\n.product-item h3 {\n  font-size: 40px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.product-item p {\n  font-size: 21px;\n  margin-bottom: 20px;\n}\n\n.product-item img {\n  max-width: 100%;\n  height: auto;\n}\n\n@media (max-width: 768px) {\n  .product-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .hero h1 {\n    font-size: 40px;\n  }\n\n  .hero h2 {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": "AAyCA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}