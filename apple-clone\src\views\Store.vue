<template>
  <div class="store-page">
    <div class="store-container">
      <h1>Apple Store 在线商店</h1>
      <div class="products-grid">
        <div v-for="product in products" :key="product.id" class="product-card">
          <img :src="product.image" :alt="product.name" class="product-image" />
          <h3>{{ product.name }}</h3>
          <p class="product-description">{{ product.description }}</p>
          <p class="product-price">¥{{ product.price }}</p>
          <button class="buy-button" @click="addToCart(product)">购买</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { products } from '../data/products'
import { mapMutations } from 'vuex'

export default {
  name: 'Store',
  data() {
    return {
      products
    }
  },
  methods: {
    ...mapMutations(['addToCart'])
  }
}
</script>

<style scoped>
.store-page {
  padding-top: 44px;
  min-height: 100vh;
  background-color: #f5f5f7;
}

.store-container {
  max-width: 1024px;
  margin: 0 auto;
  padding: 40px 20px;
}

h1 {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: 30px;
  text-align: center;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.product-card {
  background: #fff;
  border-radius: 18px;
  padding: 20px;
  text-align: center;
  transition: transform 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: contain;
  margin-bottom: 20px;
}

.product-card h3 {
  font-size: 24px;
  margin-bottom: 10px;
}

.product-description {
  color: #86868b;
  font-size: 17px;
  margin-bottom: 15px;
}

.product-price {
  font-size: 21px;
  font-weight: 600;
  margin-bottom: 20px;
}

.buy-button {
  width: 100%;
  background: #0071e3;
  color: #fff;
  border: none;
  border-radius: 980px;
  padding: 12px 24px;
  font-size: 17px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.buy-button:hover {
  background: #0077ed;
}
</style> 