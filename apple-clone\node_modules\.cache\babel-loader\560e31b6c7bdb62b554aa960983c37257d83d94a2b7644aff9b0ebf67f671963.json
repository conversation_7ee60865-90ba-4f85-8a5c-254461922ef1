{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"watch\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = {\n  class: \"cta-links\"\n};\nconst _hoisted_5 = {\n  class: \"product-grid\"\n};\nconst _hoisted_6 = {\n  class: \"product-item\"\n};\nconst _hoisted_7 = {\n  class: \"cta-links\"\n};\nconst _hoisted_8 = {\n  class: \"product-item\"\n};\nconst _hoisted_9 = {\n  class: \"cta-links\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"h1\", null, \"Apple Watch\", -1 /* HOISTED */)), _cache[3] || (_cache[3] = _createElementVNode(\"h2\", null, \"健康的未来，现在戴上。\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_router_link, {\n    to: \"/watch/series-9\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }), _createVNode(_component_router_link, {\n    to: \"/shop/watch\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"购买\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  })])]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"hero-image\"\n  }, [_createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/600x400/F5F5F7/000000?text=Apple+Watch\",\n    alt: \"Apple Watch\"\n  })], -1 /* HOISTED */))]), _createElementVNode(\"section\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"Apple Watch Series 9\", -1 /* HOISTED */)), _cache[8] || (_cache[8] = _createElementVNode(\"p\", null, \"更智能。更亮眼。更强大。\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_router_link, {\n    to: \"/watch-series-9\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [5]\n  }), _createVNode(_component_router_link, {\n    to: \"/shop/watch-series-9\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"购买\")])),\n    _: 1 /* STABLE */,\n    __: [6]\n  })]), _cache[9] || (_cache[9] = _createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/400x300/F5F5F7/000000?text=Series+9\",\n    alt: \"Apple Watch Series 9\"\n  }, null, -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_8, [_cache[12] || (_cache[12] = _createElementVNode(\"h3\", null, \"Apple Watch SE\", -1 /* HOISTED */)), _cache[13] || (_cache[13] = _createElementVNode(\"p\", null, \"功能丰富。价格亲民。\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_router_link, {\n    to: \"/watch-se\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [10]\n  }), _createVNode(_component_router_link, {\n    to: \"/shop/watch-se\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"购买\")])),\n    _: 1 /* STABLE */,\n    __: [11]\n  })]), _cache[14] || (_cache[14] = _createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/400x300/F5F5F7/000000?text=Watch+SE\",\n    alt: \"Apple Watch SE\"\n  }, null, -1 /* HOISTED */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_router_link", "to", "_cache", "src", "alt", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Watch.vue"], "sourcesContent": ["<template>\n  <div class=\"watch\">\n    <section class=\"hero\">\n      <div class=\"hero-content\">\n        <h1>Apple Watch</h1>\n        <h2>健康的未来，现在戴上。</h2>\n        <div class=\"cta-links\">\n          <router-link to=\"/watch/series-9\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/watch\" class=\"link\">购买</router-link>\n        </div>\n      </div>\n      <div class=\"hero-image\">\n        <img src=\"https://via.placeholder.com/600x400/F5F5F7/000000?text=Apple+Watch\" alt=\"Apple Watch\" />\n      </div>\n    </section>\n\n    <section class=\"product-grid\">\n      <div class=\"product-item\">\n        <h3>Apple Watch Series 9</h3>\n        <p>更智能。更亮眼。更强大。</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/watch-series-9\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/watch-series-9\" class=\"link\">购买</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=Series+9\" alt=\"Apple Watch Series 9\" />\n      </div>\n\n      <div class=\"product-item\">\n        <h3>Apple Watch SE</h3>\n        <p>功能丰富。价格亲民。</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/watch-se\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/watch-se\" class=\"link\">购买</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=Watch+SE\" alt=\"Apple Watch SE\" />\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Watch'\n}\n</script>\n\n<style scoped>\n.watch {\n  padding-top: 44px;\n}\n\n.hero {\n  background-color: #f5f5f7;\n  color: #1d1d1f;\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.hero-content {\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.hero h1 {\n  font-size: 56px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.hero h2 {\n  font-size: 28px;\n  font-weight: 400;\n  margin-bottom: 20px;\n}\n\n.cta-links {\n  display: flex;\n  justify-content: center;\n  gap: 35px;\n  margin-bottom: 20px;\n}\n\n.link {\n  color: #2997ff;\n  text-decoration: none;\n  font-size: 21px;\n}\n\n.link:hover {\n  text-decoration: underline;\n}\n\n.hero-image img {\n  max-width: 100%;\n  height: auto;\n}\n\n.product-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20px;\n  padding: 20px;\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.product-item {\n  background-color: #fbfbfd;\n  text-align: center;\n  padding: 40px 20px;\n  border-radius: 18px;\n}\n\n.product-item h3 {\n  font-size: 40px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.product-item p {\n  font-size: 21px;\n  margin-bottom: 20px;\n}\n\n.product-item img {\n  max-width: 100%;\n  height: auto;\n}\n\n@media (max-width: 768px) {\n  .product-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .hero h1 {\n    font-size: 40px;\n  }\n\n  .hero h2 {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAO;;EACPA,KAAK,EAAC;AAAM;;EACdA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;EAUjBA,KAAK,EAAC;AAAc;;EACtBA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;;uBA7B5BC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJC,mBAAA,CAYU,WAZVC,UAYU,GAXRD,mBAAA,CAOM,OAPNE,UAOM,G,0BANJF,mBAAA,CAAoB,YAAhB,aAAW,sB,0BACfA,mBAAA,CAAoB,YAAhB,aAAW,sBACfA,mBAAA,CAGM,OAHNG,UAGM,GAFJC,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC,iBAAiB;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MACnDH,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC,aAAa;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;oCAGjDP,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAAkG;IAA7FQ,GAAG,EAAC,oEAAoE;IAACC,GAAG,EAAC;6BAItFT,mBAAA,CAoBU,WApBVU,UAoBU,GAnBRV,mBAAA,CAQM,OARNW,UAQM,G,0BAPJX,mBAAA,CAA6B,YAAzB,sBAAoB,sB,0BACxBA,mBAAA,CAAmB,WAAhB,cAAY,sBACfA,mBAAA,CAGM,OAHNY,UAGM,GAFJR,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC,iBAAiB;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MACnDH,YAAA,CAAoEC,sBAAA;IAAvDC,EAAE,EAAC,sBAAsB;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;kCAExDP,mBAAA,CAAwG;IAAnGQ,GAAG,EAAC,iEAAiE;IAACC,GAAG,EAAC;iCAGjFT,mBAAA,CAQM,OARNa,UAQM,G,4BAPJb,mBAAA,CAAuB,YAAnB,gBAAc,sB,4BAClBA,mBAAA,CAAiB,WAAd,YAAU,sBACbA,mBAAA,CAGM,OAHNc,UAGM,GAFJV,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC,WAAW;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;MAC7CH,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC,gBAAgB;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;oCAElDP,mBAAA,CAAkG;IAA7FQ,GAAG,EAAC,iEAAiE;IAACC,GAAG,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}