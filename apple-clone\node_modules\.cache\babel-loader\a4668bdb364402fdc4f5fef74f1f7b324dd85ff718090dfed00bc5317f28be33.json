{"ast": null, "code": "export function getDevtoolsGlobalHook() {\n  return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nexport function getTarget() {\n  // @ts-expect-error navigator and windows are not available in all environments\n  return typeof navigator !== 'undefined' && typeof window !== 'undefined' ? window : typeof globalThis !== 'undefined' ? globalThis : {};\n}\nexport const isProxyAvailable = typeof Proxy === 'function';", "map": {"version": 3, "names": ["getDevtoolsGlobalHook", "get<PERSON><PERSON><PERSON>", "__VUE_DEVTOOLS_GLOBAL_HOOK__", "navigator", "window", "globalThis", "isProxyAvailable", "Proxy"], "sources": ["E:/cursor/apple-clone/node_modules/@vue/devtools-api/lib/esm/env.js"], "sourcesContent": ["export function getDevtoolsGlobalHook() {\n    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nexport function getTarget() {\n    // @ts-expect-error navigator and windows are not available in all environments\n    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')\n        ? window\n        : typeof globalThis !== 'undefined'\n            ? globalThis\n            : {};\n}\nexport const isProxyAvailable = typeof Proxy === 'function';\n"], "mappings": "AAAA,OAAO,SAASA,qBAAqBA,CAAA,EAAG;EACpC,OAAOC,SAAS,CAAC,CAAC,CAACC,4BAA4B;AACnD;AACA,OAAO,SAASD,SAASA,CAAA,EAAG;EACxB;EACA,OAAQ,OAAOE,SAAS,KAAK,WAAW,IAAI,OAAOC,MAAM,KAAK,WAAW,GACnEA,MAAM,GACN,OAAOC,UAAU,KAAK,WAAW,GAC7BA,UAAU,GACV,CAAC,CAAC;AAChB;AACA,OAAO,MAAMC,gBAAgB,GAAG,OAAOC,KAAK,KAAK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}