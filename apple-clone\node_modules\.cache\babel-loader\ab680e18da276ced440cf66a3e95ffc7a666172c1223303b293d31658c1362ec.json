{"ast": null, "code": "import { createStore } from 'vuex';\nexport default createStore({\n  state: {\n    cart: [],\n    searchQuery: '',\n    isSearchOpen: false\n  },\n  mutations: {\n    addToCart(state, product) {\n      state.cart.push(product);\n    },\n    removeFromCart(state, productId) {\n      state.cart = state.cart.filter(item => item.id !== productId);\n    },\n    setSearchQuery(state, query) {\n      state.searchQuery = query;\n    },\n    toggleSearch(state) {\n      state.isSearchOpen = !state.isSearchOpen;\n    }\n  },\n  actions: {\n    addToCart({\n      commit\n    }, product) {\n      commit('addToCart', product);\n    },\n    removeFromCart({\n      commit\n    }, productId) {\n      commit('removeFromCart', productId);\n    },\n    updateSearch({\n      commit\n    }, query) {\n      commit('setSearchQuery', query);\n    },\n    toggleSearch({\n      commit\n    }) {\n      commit('toggleSearch');\n    }\n  },\n  getters: {\n    cartItemCount: state => state.cart.length,\n    cartTotal: state => state.cart.reduce((total, item) => total + item.price, 0)\n  }\n});", "map": {"version": 3, "names": ["createStore", "state", "cart", "searchQuery", "isSearchOpen", "mutations", "addToCart", "product", "push", "removeFromCart", "productId", "filter", "item", "id", "setSearch<PERSON>uery", "query", "toggleSearch", "actions", "commit", "updateSearch", "getters", "cartItemCount", "length", "cartTotal", "reduce", "total", "price"], "sources": ["E:/cursor/apple-clone/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\r\n\r\nexport default createStore({\r\n  state: {\r\n    cart: [],\r\n    searchQuery: '',\r\n    isSearchOpen: false\r\n  },\r\n  mutations: {\r\n    addToCart(state, product) {\r\n      state.cart.push(product)\r\n    },\r\n    removeFromCart(state, productId) {\r\n      state.cart = state.cart.filter(item => item.id !== productId)\r\n    },\r\n    setSearchQuery(state, query) {\r\n      state.searchQuery = query\r\n    },\r\n    toggleSearch(state) {\r\n      state.isSearchOpen = !state.isSearchOpen\r\n    }\r\n  },\r\n  actions: {\r\n    addToCart({ commit }, product) {\r\n      commit('addToCart', product)\r\n    },\r\n    removeFromCart({ commit }, productId) {\r\n      commit('removeFromCart', productId)\r\n    },\r\n    updateSearch({ commit }, query) {\r\n      commit('setSearchQuery', query)\r\n    },\r\n    toggleSearch({ commit }) {\r\n      commit('toggleSearch')\r\n    }\r\n  },\r\n  getters: {\r\n    cartItemCount: state => state.cart.length,\r\n    cartTotal: state => state.cart.reduce((total, item) => total + item.price, 0)\r\n  }\r\n}) "], "mappings": "AAAA,SAASA,WAAW,QAAQ,MAAM;AAElC,eAAeA,WAAW,CAAC;EACzBC,KAAK,EAAE;IACLC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE;IACTC,SAASA,CAACL,KAAK,EAAEM,OAAO,EAAE;MACxBN,KAAK,CAACC,IAAI,CAACM,IAAI,CAACD,OAAO,CAAC;IAC1B,CAAC;IACDE,cAAcA,CAACR,KAAK,EAAES,SAAS,EAAE;MAC/BT,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,CAACS,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKH,SAAS,CAAC;IAC/D,CAAC;IACDI,cAAcA,CAACb,KAAK,EAAEc,KAAK,EAAE;MAC3Bd,KAAK,CAACE,WAAW,GAAGY,KAAK;IAC3B,CAAC;IACDC,YAAYA,CAACf,KAAK,EAAE;MAClBA,KAAK,CAACG,YAAY,GAAG,CAACH,KAAK,CAACG,YAAY;IAC1C;EACF,CAAC;EACDa,OAAO,EAAE;IACPX,SAASA,CAAC;MAAEY;IAAO,CAAC,EAAEX,OAAO,EAAE;MAC7BW,MAAM,CAAC,WAAW,EAAEX,OAAO,CAAC;IAC9B,CAAC;IACDE,cAAcA,CAAC;MAAES;IAAO,CAAC,EAAER,SAAS,EAAE;MACpCQ,MAAM,CAAC,gBAAgB,EAAER,SAAS,CAAC;IACrC,CAAC;IACDS,YAAYA,CAAC;MAAED;IAAO,CAAC,EAAEH,KAAK,EAAE;MAC9BG,MAAM,CAAC,gBAAgB,EAAEH,KAAK,CAAC;IACjC,CAAC;IACDC,YAAYA,CAAC;MAAEE;IAAO,CAAC,EAAE;MACvBA,MAAM,CAAC,cAAc,CAAC;IACxB;EACF,CAAC;EACDE,OAAO,EAAE;IACPC,aAAa,EAAEpB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACoB,MAAM;IACzCC,SAAS,EAAEtB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACsB,MAAM,CAAC,CAACC,KAAK,EAAEb,IAAI,KAAKa,KAAK,GAAGb,IAAI,CAACc,KAAK,EAAE,CAAC;EAC9E;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}