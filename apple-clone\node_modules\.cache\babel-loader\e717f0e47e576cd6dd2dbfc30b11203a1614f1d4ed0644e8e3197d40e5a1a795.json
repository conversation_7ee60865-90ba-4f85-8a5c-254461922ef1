{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '../assets/iphone-15-pro.jpg';\nimport _imports_1 from '../assets/macbook-air.jpg';\nimport _imports_2 from '../assets/ipad-pro.jpg';\nconst _hoisted_1 = {\n  class: \"home\"\n};\nconst _hoisted_2 = {\n  class: \"hero\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = {\n  class: \"cta-links\"\n};\nconst _hoisted_5 = {\n  class: \"product-grid\"\n};\nconst _hoisted_6 = {\n  class: \"product-item\"\n};\nconst _hoisted_7 = {\n  class: \"cta-links\"\n};\nconst _hoisted_8 = {\n  class: \"product-item\"\n};\nconst _hoisted_9 = {\n  class: \"cta-links\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"h1\", null, \"iPhone 15 Pro\", -1 /* HOISTED */)), _cache[3] || (_cache[3] = _createElementVNode(\"h2\", null, \"钛金属。超强大。超Pro。\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_router_link, {\n    to: \"/iphone-15-pro\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }), _createVNode(_component_router_link, {\n    to: \"/shop/iphone-15-pro\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"购买\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  })])]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"hero-image\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"iPhone 15 Pro\"\n  })], -1 /* HOISTED */))]), _createElementVNode(\"section\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"MacBook Air\", -1 /* HOISTED */)), _cache[8] || (_cache[8] = _createElementVNode(\"p\", null, \"搭载 M2 芯片\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_router_link, {\n    to: \"/macbook-air\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [5]\n  }), _createVNode(_component_router_link, {\n    to: \"/shop/macbook-air\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"购买\")])),\n    _: 1 /* STABLE */,\n    __: [6]\n  })]), _cache[9] || (_cache[9] = _createElementVNode(\"img\", {\n    src: _imports_1,\n    alt: \"MacBook Air\"\n  }, null, -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_8, [_cache[12] || (_cache[12] = _createElementVNode(\"h3\", null, \"iPad Pro\", -1 /* HOISTED */)), _cache[13] || (_cache[13] = _createElementVNode(\"p\", null, \"搭载 M2 芯片\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_router_link, {\n    to: \"/ipad-pro\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [10]\n  }), _createVNode(_component_router_link, {\n    to: \"/shop/ipad-pro\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"购买\")])),\n    _: 1 /* STABLE */,\n    __: [11]\n  })]), _cache[14] || (_cache[14] = _createElementVNode(\"img\", {\n    src: _imports_2,\n    alt: \"iPad Pro\"\n  }, null, -1 /* HOISTED */))])])]);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_router_link", "to", "_cache", "src", "alt", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <section class=\"hero\">\r\n      <div class=\"hero-content\">\r\n        <h1>iPhone 15 Pro</h1>\r\n        <h2>钛金属。超强大。超Pro。</h2>\r\n        <div class=\"cta-links\">\r\n          <router-link to=\"/iphone-15-pro\" class=\"link\">了解更多</router-link>\r\n          <router-link to=\"/shop/iphone-15-pro\" class=\"link\">购买</router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"hero-image\">\r\n        <img src=\"../assets/iphone-15-pro.jpg\" alt=\"iPhone 15 Pro\" />\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"product-grid\">\r\n      <div class=\"product-item\">\r\n        <h3>MacBook Air</h3>\r\n        <p>搭载 M2 芯片</p>\r\n        <div class=\"cta-links\">\r\n          <router-link to=\"/macbook-air\" class=\"link\">了解更多</router-link>\r\n          <router-link to=\"/shop/macbook-air\" class=\"link\">购买</router-link>\r\n        </div>\r\n        <img src=\"../assets/macbook-air.jpg\" alt=\"MacBook Air\" />\r\n      </div>\r\n\r\n      <div class=\"product-item\">\r\n        <h3>iPad Pro</h3>\r\n        <p>搭载 M2 芯片</p>\r\n        <div class=\"cta-links\">\r\n          <router-link to=\"/ipad-pro\" class=\"link\">了解更多</router-link>\r\n          <router-link to=\"/shop/ipad-pro\" class=\"link\">购买</router-link>\r\n        </div>\r\n        <img src=\"../assets/ipad-pro.jpg\" alt=\"iPad Pro\" />\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Home'\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding-top: 44px;\r\n}\r\n\r\n.hero {\r\n  background-color: #000;\r\n  color: #fff;\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n}\r\n\r\n.hero-content {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.hero h1 {\r\n  font-size: 56px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.hero h2 {\r\n  font-size: 28px;\r\n  font-weight: 400;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.cta-links {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 35px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.link {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 21px;\r\n}\r\n\r\n.link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.hero-image img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n.product-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  padding: 20px;\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.product-item {\r\n  background-color: #fbfbfd;\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  border-radius: 18px;\r\n}\r\n\r\n.product-item h3 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.product-item p {\r\n  font-size: 21px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.product-item img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .product-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .hero h1 {\r\n    font-size: 40px;\r\n  }\r\n\r\n  .hero h2 {\r\n    font-size: 24px;\r\n  }\r\n}\r\n</style> "], "mappings": ";OAYaA,UAAiC;OAYjCC,UAA+B;OAU/BC,UAA4B;;EAjClCC,KAAK,EAAC;AAAM;;EACNA,KAAK,EAAC;AAAM;;EACdA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;EAUjBA,KAAK,EAAC;AAAc;;EACtBA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAW;;;uBA7B5BC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJC,mBAAA,CAYU,WAZVC,UAYU,GAXRD,mBAAA,CAOM,OAPNE,UAOM,G,0BANJF,mBAAA,CAAsB,YAAlB,eAAa,sB,0BACjBA,mBAAA,CAAsB,YAAlB,eAAa,sBACjBA,mBAAA,CAGM,OAHNG,UAGM,GAFJC,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC,gBAAgB;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAClDH,YAAA,CAAmEC,sBAAA;IAAtDC,EAAE,EAAC,qBAAqB;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;oCAGzDP,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAA6D;IAAxDQ,GAAiC,EAAjCd,UAAiC;IAACe,GAAG,EAAC;6BAI/CT,mBAAA,CAoBU,WApBVU,UAoBU,GAnBRV,mBAAA,CAQM,OARNW,UAQM,G,0BAPJX,mBAAA,CAAoB,YAAhB,aAAW,sB,0BACfA,mBAAA,CAAe,WAAZ,UAAQ,sBACXA,mBAAA,CAGM,OAHNY,UAGM,GAFJR,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC,cAAc;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAChDH,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC,mBAAmB;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;kCAErDP,mBAAA,CAAyD;IAApDQ,GAA+B,EAA/Bb,UAA+B;IAACc,GAAG,EAAC;iCAG3CT,mBAAA,CAQM,OARNa,UAQM,G,4BAPJb,mBAAA,CAAiB,YAAb,UAAQ,sB,4BACZA,mBAAA,CAAe,WAAZ,UAAQ,sBACXA,mBAAA,CAGM,OAHNc,UAGM,GAFJV,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC,WAAW;IAACT,KAAK,EAAC;;sBAAO,MAAIU,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;MAC7CH,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC,gBAAgB;IAACT,KAAK,EAAC;;sBAAO,MAAEU,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;oCAElDP,mBAAA,CAAmD;IAA9CQ,GAA4B,EAA5BZ,UAA4B;IAACa,GAAG,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}