<template>
  <div class="bag-page">
    <div class="bag-container">
      <h1>购物袋</h1>
      
      <div v-if="cart.length === 0" class="empty-bag">
        <p>您的购物袋是空的。</p>
        <router-link to="/store" class="continue-shopping">继续购物</router-link>
      </div>

      <div v-else class="bag-content">
        <div class="cart-items">
          <div v-for="item in cart" :key="item.id" class="cart-item">
            <img :src="item.image" :alt="item.name" class="item-image" />
            <div class="item-details">
              <h3>{{ item.name }}</h3>
              <p class="item-price">¥{{ item.price }}</p>
              <div class="item-actions">
                <button class="remove-button" @click="removeFromCart(item.id)">移除</button>
              </div>
            </div>
          </div>
        </div>

        <div class="order-summary">
          <h2>订单摘要</h2>
          <div class="summary-row">
            <span>小计</span>
            <span>¥{{ cartTotal }}</span>
          </div>
          <div class="summary-row">
            <span>运费</span>
            <span>免费</span>
          </div>
          <div class="summary-row total">
            <span>总计</span>
            <span>¥{{ cartTotal }}</span>
          </div>
          <button class="checkout-button">结账</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'

export default {
  name: 'Bag',
  computed: {
    ...mapState(['cart']),
    ...mapGetters(['cartTotal'])
  },
  methods: {
    ...mapMutations(['removeFromCart'])
  }
}
</script>

<style scoped>
.bag-page {
  padding-top: 44px;
  min-height: 100vh;
  background-color: #f5f5f7;
}

.bag-container {
  max-width: 1024px;
  margin: 0 auto;
  padding: 40px 20px;
}

h1 {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: 30px;
}

.empty-bag {
  text-align: center;
  padding: 60px 0;
}

.empty-bag p {
  font-size: 21px;
  color: #86868b;
  margin-bottom: 20px;
}

.continue-shopping {
  color: #2997ff;
  text-decoration: none;
  font-size: 17px;
}

.continue-shopping:hover {
  text-decoration: underline;
}

.bag-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
}

.cart-items {
  background: #fff;
  border-radius: 18px;
  padding: 20px;
}

.cart-item {
  display: flex;
  gap: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #d2d2d7;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 120px;
  height: 120px;
  object-fit: contain;
}

.item-details {
  flex: 1;
}

.item-details h3 {
  font-size: 17px;
  margin-bottom: 10px;
}

.item-price {
  color: #1d1d1f;
  font-size: 17px;
  margin-bottom: 15px;
}

.remove-button {
  background: none;
  border: none;
  color: #2997ff;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
}

.remove-button:hover {
  text-decoration: underline;
}

.order-summary {
  background: #fff;
  border-radius: 18px;
  padding: 20px;
  height: fit-content;
}

.order-summary h2 {
  font-size: 24px;
  margin-bottom: 20px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 17px;
}

.summary-row.total {
  font-weight: 600;
  font-size: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #d2d2d7;
}

.checkout-button {
  width: 100%;
  background: #0071e3;
  color: #fff;
  border: none;
  border-radius: 980px;
  padding: 18px 31px;
  font-size: 17px;
  font-weight: 400;
  margin-top: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.checkout-button:hover {
  background: #0077ed;
}

@media (max-width: 768px) {
  .bag-content {
    grid-template-columns: 1fr;
  }
}
</style> 