{"ast": null, "code": "import { mapState, mapGetters, mapMutations } from 'vuex';\nimport Search from '../Search/Search.vue';\nexport default {\n  name: 'Navbar',\n  components: {\n    Search\n  },\n  data() {\n    return {\n      isScrolled: false,\n      isMenuOpen: false\n    };\n  },\n  computed: {\n    ...mapState(['isSearchOpen']),\n    ...mapGetters(['cartItemCount'])\n  },\n  mounted() {\n    window.addEventListener('scroll', this.handleScroll);\n  },\n  beforeUnmount() {\n    window.removeEventListener('scroll', this.handleScroll);\n  },\n  methods: {\n    ...mapMutations(['toggleSearch']),\n    handleScroll() {\n      this.isScrolled = window.scrollY > 0;\n    },\n    toggleMenu() {\n      this.isMenuOpen = !this.isMenuOpen;\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapGetters", "mapMutations", "Search", "name", "components", "data", "isScrolled", "isMenuOpen", "computed", "mounted", "window", "addEventListener", "handleScroll", "beforeUnmount", "removeEventListener", "methods", "scrollY", "toggleMenu"], "sources": ["E:\\cursor\\apple-clone\\src\\components\\Navbar\\Navbar.vue"], "sourcesContent": ["<template>\r\n  <nav class=\"navbar\" :class=\"{ scrolled: isScrolled }\">\r\n    <div class=\"nav-content\">\r\n      <div class=\"nav-left\">\r\n        <router-link to=\"/\" class=\"apple-logo\">\r\n          <svg height=\"44\" viewBox=\"0 0 14 44\" width=\"14\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"m13.0729 17.6825a3.61 3.61 0 0 0 -1.7248 3.0365 3.5132 3.5132 0 0 0 2.1379 3.2223 8.394 8.394 0 0 1 -1.0948 2.2618c-.6816.9812-1.3943 1.9623-2.4787 1.9623s-1.3633-.63-2.613-.63c-1.2187 0-1.6525.6507-2.644.6507s-1.6834-.9089-2.4787-2.0243a9.7842 9.7842 0 0 1 -1.6628-5.2776c0-3.0984 2.014-4.7405 3.9969-4.7405 1.0535 0 1.9314.6919 2.5924.6919.63 0 1.6112-.7333 2.8092-.7333a3.7579 3.7579 0 0 1 3.1604 1.5802zm-3.7284-2.8918a1.3865 1.3865 0 0 0 -.2179-.5388 1.655 1.655 0 0 0 -1.4728-.5169c-.0306-.0039-.0613-.0039-.0919-.0039-.6299 0-1.6112.7333-2.8092.7333-1.2187 0-1.6525-.6507-2.644-.6507s-1.6834.9089-2.4787 2.0243a9.7842 9.7842 0 0 0 -1.6628 5.2776c0 3.0984 2.014 4.7405 3.9969 4.7405 1.0535 0 1.9314-.6919 2.5924-.6919.63 0 1.6112.7333 2.8092.7333 1.2187 0 1.6525-.6507 2.644-.6507s1.6834-.9089 2.4787-2.0243a9.7842 9.7842 0 0 0 1.6628-5.2776c0-3.0984-2.014-4.7405-3.9969-4.7405-1.0535 0-1.9314.6919-2.5924.6919-.63 0-1.6112-.7333-2.8092-.7333-1.2187 0-1.6525.6507-2.644.6507s-1.6834-.9089-2.4787-2.0243a9.7842 9.7842 0 0 0 -1.6628 5.2776c0 3.0984 2.014 4.7405 3.9969 4.7405 1.0535 0 1.9314-.6919 2.5924-.6919.63 0 1.6112.7333 2.8092.7333 1.2187 0 1.6525-.6507 2.644-.6507s1.6834-.9089 2.4787-2.0243a9.7842 9.7842 0 0 0 1.6628-5.2776c0-3.0984-2.014-4.7405-3.9969-4.7405-1.0535 0-1.9314.6919-2.5924.6919-.63 0-1.6112-.7333-2.8092-.7333-1.2187 0-1.6525.6507-2.644.6507s-1.6834-.9089-2.4787-2.0243a9.7842 9.7842 0 0 0 -1.6628 5.2776c0 3.0984 2.014 4.7405 3.9969 4.7405 1.0535 0 1.9314-.6919 2.5924-.6919.63 0 1.6112.7333 2.8092.7333 1.2187 0 1.6525-.6507 2.644-.6507s1.6834-.9089 2.4787-2.0243a9.7842 9.7842 0 0 0 1.6628-5.2776c0-3.0984-2.014-4.7405-3.9969-4.7405-1.0535 0-1.9314.6919-2.5924.6919-.63 0-1.6112-.7333-2.8092-.7333-1.2187 0-1.6525.6507-2.644.6507s-1.6834-.9089-2.4787-2.0243a9.7842 9.7842 0 0 0 -1.6628 5.2776c0 3.0984 2.014 4.7405 3.9969 4.7405 1.0535 0 1.9314-.6919 2.5924-.6919.63 0 1.6112.7333 2.8092.7333 1.2187 0 1.6525-.6507 2.644-.6507s1.6834-.9089 2.4787-2.0243a9.7842 9.7842 0 0 0 1.6628-5.2776c0-3.0984-2.014-4.7405-3.9969-4.7405-1.0535 0-1.9314.6919-2.5924.6919-.63 0-1.6112-.7333-2.8092-.7333z\" fill=\"#f5f5f7\"/>\r\n          </svg>\r\n        </router-link>\r\n      </div>\r\n      \r\n      <div class=\"nav-center\" :class=\"{ active: isMenuOpen }\">\r\n        <router-link to=\"/store\" class=\"nav-item\">商店</router-link>\r\n        <router-link to=\"/mac\" class=\"nav-item\">Mac</router-link>\r\n        <router-link to=\"/ipad\" class=\"nav-item\">iPad</router-link>\r\n        <router-link to=\"/iphone\" class=\"nav-item\">iPhone</router-link>\r\n        <router-link to=\"/watch\" class=\"nav-item\">Watch</router-link>\r\n        <router-link to=\"/airpods\" class=\"nav-item\">AirPods</router-link>\r\n        <router-link to=\"/tv-home\" class=\"nav-item\">电视和家庭</router-link>\r\n        <router-link to=\"/entertainment\" class=\"nav-item\">娱乐</router-link>\r\n        <router-link to=\"/accessories\" class=\"nav-item\">配件</router-link>\r\n        <router-link to=\"/support\" class=\"nav-item\">技术支持</router-link>\r\n      </div>\r\n\r\n      <div class=\"nav-right\">\r\n        <button class=\"search-icon\" @click=\"toggleSearch\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"44\" viewBox=\"0 0 15 44\">\r\n            <path d=\"M14.298,27.202l-3.87-3.87c0.701-0.929,1.122-2.081,1.122-3.332c0-3.06-2.489-5.55-5.55-5.55c-3.06,0-5.55,2.49-5.55,5.55 c0,3.061,2.49,5.55,5.55,5.55c1.251,0,2.403-0.421,3.332-1.122l3.87,3.87c0.151,0.151,0.35,0.228,0.548,0.228 s0.396-0.076,0.548-0.228C14.601,27.995,14.601,27.505,14.298,27.202z M1.55,20c0-2.454,1.997-4.45,4.45-4.45 c2.454,0,4.45,1.997,4.45,4.45S8.454,24.45,6,24.45C3.546,24.45,1.55,22.454,1.55,20z\" fill=\"#f5f5f7\"/>\r\n          </svg>\r\n        </button>\r\n        <router-link to=\"/bag\" class=\"bag-icon\">\r\n          <svg height=\"44\" viewBox=\"0 0 14 44\" width=\"14\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"m11.3535 16.0283h-1.0205a3.4229 3.4229 0 0 0 -3.333-2.9648 3.4229 3.4229 0 0 0 -3.333 2.9648h-1.0205a2.1184 2.1184 0 0 0 -2.1172 2.1162v7.7155a2.1186 2.1186 0 0 0 2.1172 2.1162h8.7338a2.1186 2.1186 0 0 0 2.1172-2.1162v-7.7155a2.1184 2.1184 0 0 0 -2.1172-2.1162zm-4.3535-1.8652a2.3169 2.3169 0 0 1 2.2222 1.8652h-4.4444a2.3169 2.3169 0 0 1 2.2222-1.8652zm5.37 11.6969a1.0182 1.0182 0 0 1 -1.0166 1.0171h-8.7338a1.0182 1.0182 0 0 1 -1.0166-1.0171v-7.7155a1.0178 1.0178 0 0 1 1.0166-1.0166h8.7338a1.0178 1.0178 0 0 1 1.0166 1.0166z\" fill=\"#f5f5f7\"/>\r\n          </svg>\r\n          <span class=\"cart-count\" v-if=\"cartItemCount\">{{ cartItemCount }}</span>\r\n        </router-link>\r\n        <button class=\"menu-toggle\" :class=\"{ active: isMenuOpen }\" @click=\"toggleMenu\">\r\n          <span></span>\r\n          <span></span>\r\n          <span></span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <Search />\r\n  </nav>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters, mapMutations } from 'vuex'\r\nimport Search from '../Search/Search.vue'\r\n\r\nexport default {\r\n  name: 'Navbar',\r\n  components: {\r\n    Search\r\n  },\r\n  data() {\r\n    return {\r\n      isScrolled: false,\r\n      isMenuOpen: false\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['isSearchOpen']),\r\n    ...mapGetters(['cartItemCount'])\r\n  },\r\n  mounted() {\r\n    window.addEventListener('scroll', this.handleScroll)\r\n  },\r\n  beforeUnmount() {\r\n    window.removeEventListener('scroll', this.handleScroll)\r\n  },\r\n  methods: {\r\n    ...mapMutations(['toggleSearch']),\r\n    handleScroll() {\r\n      this.isScrolled = window.scrollY > 0\r\n    },\r\n    toggleMenu() {\r\n      this.isMenuOpen = !this.isMenuOpen\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n@import './Navbar.css';\r\n\r\n.cart-count {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: #ff3b30;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n</style> "], "mappings": "AAgDA,SAASA,QAAQ,EAAEC,UAAU,EAAEC,YAAW,QAAS,MAAK;AACxD,OAAOC,MAAK,MAAO,sBAAqB;AAExC,eAAe;EACbC,IAAI,EAAE,QAAQ;EACdC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;IACd;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,GAAGT,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;IAC7B,GAAGC,UAAU,CAAC,CAAC,eAAe,CAAC;EACjC,CAAC;EACDS,OAAOA,CAAA,EAAG;IACRC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY;EACrD,CAAC;EACDC,aAAaA,CAAA,EAAG;IACdH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY;EACxD,CAAC;EACDG,OAAO,EAAE;IACP,GAAGd,YAAY,CAAC,CAAC,cAAc,CAAC,CAAC;IACjCW,YAAYA,CAAA,EAAG;MACb,IAAI,CAACN,UAAS,GAAII,MAAM,CAACM,OAAM,GAAI;IACrC,CAAC;IACDC,UAAUA,CAAA,EAAG;MACX,IAAI,CAACV,UAAS,GAAI,CAAC,IAAI,CAACA,UAAS;IACnC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}