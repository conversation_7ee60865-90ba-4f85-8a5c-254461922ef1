{"ast": null, "code": "import { mapState, mapGetters, mapMutations } from 'vuex';\nexport default {\n  name: 'Bag',\n  computed: {\n    ...mapState(['cart']),\n    ...mapGetters(['cartTotal'])\n  },\n  methods: {\n    ...mapMutations(['removeFromCart'])\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapGetters", "mapMutations", "name", "computed", "methods"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Bag.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bag-page\">\r\n    <div class=\"bag-container\">\r\n      <h1>购物袋</h1>\r\n      \r\n      <div v-if=\"cart.length === 0\" class=\"empty-bag\">\r\n        <p>您的购物袋是空的。</p>\r\n        <router-link to=\"/store\" class=\"continue-shopping\">继续购物</router-link>\r\n      </div>\r\n\r\n      <div v-else class=\"bag-content\">\r\n        <div class=\"cart-items\">\r\n          <div v-for=\"item in cart\" :key=\"item.id\" class=\"cart-item\">\r\n            <img :src=\"item.image\" :alt=\"item.name\" class=\"item-image\" />\r\n            <div class=\"item-details\">\r\n              <h3>{{ item.name }}</h3>\r\n              <p class=\"item-price\">¥{{ item.price }}</p>\r\n              <div class=\"item-actions\">\r\n                <button class=\"remove-button\" @click=\"removeFromCart(item.id)\">移除</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"order-summary\">\r\n          <h2>订单摘要</h2>\r\n          <div class=\"summary-row\">\r\n            <span>小计</span>\r\n            <span>¥{{ cartTotal }}</span>\r\n          </div>\r\n          <div class=\"summary-row\">\r\n            <span>运费</span>\r\n            <span>免费</span>\r\n          </div>\r\n          <div class=\"summary-row total\">\r\n            <span>总计</span>\r\n            <span>¥{{ cartTotal }}</span>\r\n          </div>\r\n          <button class=\"checkout-button\">结账</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters, mapMutations } from 'vuex'\r\n\r\nexport default {\r\n  name: 'Bag',\r\n  computed: {\r\n    ...mapState(['cart']),\r\n    ...mapGetters(['cartTotal'])\r\n  },\r\n  methods: {\r\n    ...mapMutations(['removeFromCart'])\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.bag-page {\r\n  padding-top: 44px;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f7;\r\n}\r\n\r\n.bag-container {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n  padding: 40px 20px;\r\n}\r\n\r\nh1 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.empty-bag {\r\n  text-align: center;\r\n  padding: 60px 0;\r\n}\r\n\r\n.empty-bag p {\r\n  font-size: 21px;\r\n  color: #86868b;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.continue-shopping {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 17px;\r\n}\r\n\r\n.continue-shopping:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.bag-content {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 40px;\r\n}\r\n\r\n.cart-items {\r\n  background: #fff;\r\n  border-radius: 18px;\r\n  padding: 20px;\r\n}\r\n\r\n.cart-item {\r\n  display: flex;\r\n  gap: 20px;\r\n  padding: 20px 0;\r\n  border-bottom: 1px solid #d2d2d7;\r\n}\r\n\r\n.cart-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  object-fit: contain;\r\n}\r\n\r\n.item-details {\r\n  flex: 1;\r\n}\r\n\r\n.item-details h3 {\r\n  font-size: 17px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.item-price {\r\n  color: #1d1d1f;\r\n  font-size: 17px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.remove-button {\r\n  background: none;\r\n  border: none;\r\n  color: #2997ff;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  padding: 0;\r\n}\r\n\r\n.remove-button:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.order-summary {\r\n  background: #fff;\r\n  border-radius: 18px;\r\n  padding: 20px;\r\n  height: fit-content;\r\n}\r\n\r\n.order-summary h2 {\r\n  font-size: 24px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  font-size: 17px;\r\n}\r\n\r\n.summary-row.total {\r\n  font-weight: 600;\r\n  font-size: 20px;\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #d2d2d7;\r\n}\r\n\r\n.checkout-button {\r\n  width: 100%;\r\n  background: #0071e3;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 980px;\r\n  padding: 18px 31px;\r\n  font-size: 17px;\r\n  font-weight: 400;\r\n  margin-top: 20px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.checkout-button:hover {\r\n  background: #0077ed;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .bag-content {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n</style> "], "mappings": "AA8CA,SAASA,QAAQ,EAAEC,UAAU,EAAEC,YAAW,QAAS,MAAK;AAExD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE;IACR,GAAGJ,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;IACrB,GAAGC,UAAU,CAAC,CAAC,WAAW,CAAC;EAC7B,CAAC;EACDI,OAAO,EAAE;IACP,GAAGH,YAAY,CAAC,CAAC,gBAAgB,CAAC;EACpC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}