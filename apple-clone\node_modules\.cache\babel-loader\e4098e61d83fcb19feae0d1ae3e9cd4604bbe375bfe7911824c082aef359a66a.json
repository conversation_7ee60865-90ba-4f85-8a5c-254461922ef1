{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelText as _vModelText, withDirectives as _withDirectives, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"search-container\"\n};\nconst _hoisted_2 = {\n  class: \"search-header\"\n};\nconst _hoisted_3 = {\n  class: \"search-input-wrapper\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"search-results\"\n};\nconst _hoisted_5 = {\n  class: \"quick-links\"\n};\nconst _hoisted_6 = {\n  class: \"links-grid\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"search-overlay\", {\n      active: _ctx.isSearchOpen\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[3] || (_cache[3] = _createElementVNode(\"svg\", {\n    class: \"search-icon\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"15\",\n    height: \"44\",\n    viewBox: \"0 0 15 44\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M14.298,27.202l-3.87-3.87c0.701-0.929,1.122-2.081,1.122-3.332c0-3.06-2.489-5.55-5.55-5.55c-3.06,0-5.55,2.49-5.55,5.55 c0,3.061,2.49,5.55,5.55,5.55c1.251,0,2.403-0.421,3.332-1.122l3.87,3.87c0.151,0.151,0.35,0.228,0.548,0.228 s0.396-0.076,0.548-0.228C14.601,27.995,14.601,27.505,14.298,27.202z M1.55,20c0-2.454,1.997-4.45,4.45-4.45 c2.454,0,4.45,1.997,4.45,4.45S8.454,24.45,6,24.45C3.546,24.45,1.55,22.454,1.55,20z\",\n    fill: \"#86868b\"\n  })], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.searchQuery = $event),\n    placeholder: \"搜索 apple.com.cn\",\n    onInput: _cache[1] || (_cache[1] = (...args) => $options.handleSearch && $options.handleSearch(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.searchQuery]])]), _createElementVNode(\"button\", {\n    class: \"close-button\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.closeSearch && $options.closeSearch(...args))\n  }, \"取消\")]), $data.searchQuery ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[12] || (_cache[12] = _createElementVNode(\"h3\", null, \"快速链接\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_router_link, {\n    to: \"/store\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"访问 Apple Store 在线商店\")])),\n    _: 1 /* STABLE */,\n    __: [4]\n  }), _createVNode(_component_router_link, {\n    to: \"/accessories\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"配件\")])),\n    _: 1 /* STABLE */,\n    __: [5]\n  }), _createVNode(_component_router_link, {\n    to: \"/airpods\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"AirPods\")])),\n    _: 1 /* STABLE */,\n    __: [6]\n  }), _createVNode(_component_router_link, {\n    to: \"/airtag\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"AirTag\")])),\n    _: 1 /* STABLE */,\n    __: [7]\n  }), _createVNode(_component_router_link, {\n    to: \"/apple-tv\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"Apple TV\")])),\n    _: 1 /* STABLE */,\n    __: [8]\n  }), _createVNode(_component_router_link, {\n    to: \"/ipad\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"iPad\")])),\n    _: 1 /* STABLE */,\n    __: [9]\n  }), _createVNode(_component_router_link, {\n    to: \"/iphone\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"iPhone\")])),\n    _: 1 /* STABLE */,\n    __: [10]\n  }), _createVNode(_component_router_link, {\n    to: \"/mac\",\n    class: \"quick-link\"\n  }, {\n    default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"Mac\")])),\n    _: 1 /* STABLE */,\n    __: [11]\n  })])])])) : _createCommentVNode(\"v-if\", true)])], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "active", "_ctx", "isSearchOpen", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "xmlns", "width", "height", "viewBox", "d", "fill", "type", "$data", "searchQuery", "$event", "placeholder", "onInput", "_cache", "args", "$options", "handleSearch", "onClick", "closeSearch", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_router_link", "to"], "sources": ["E:\\cursor\\apple-clone\\src\\components\\Search\\Search.vue"], "sourcesContent": ["<template>\r\n  <div class=\"search-overlay\" :class=\"{ active: isSearchOpen }\">\r\n    <div class=\"search-container\">\r\n      <div class=\"search-header\">\r\n        <div class=\"search-input-wrapper\">\r\n          <svg class=\"search-icon\" xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"44\" viewBox=\"0 0 15 44\">\r\n            <path d=\"M14.298,27.202l-3.87-3.87c0.701-0.929,1.122-2.081,1.122-3.332c0-3.06-2.489-5.55-5.55-5.55c-3.06,0-5.55,2.49-5.55,5.55 c0,3.061,2.49,5.55,5.55,5.55c1.251,0,2.403-0.421,3.332-1.122l3.87,3.87c0.151,0.151,0.35,0.228,0.548,0.228 s0.396-0.076,0.548-0.228C14.601,27.995,14.601,27.505,14.298,27.202z M1.55,20c0-2.454,1.997-4.45,4.45-4.45 c2.454,0,4.45,1.997,4.45,4.45S8.454,24.45,6,24.45C3.546,24.45,1.55,22.454,1.55,20z\" fill=\"#86868b\"/>\r\n          </svg>\r\n          <input\r\n            type=\"text\"\r\n            v-model=\"searchQuery\"\r\n            placeholder=\"搜索 apple.com.cn\"\r\n            @input=\"handleSearch\"\r\n          />\r\n        </div>\r\n        <button class=\"close-button\" @click=\"closeSearch\">取消</button>\r\n      </div>\r\n      \r\n      <div class=\"search-results\" v-if=\"searchQuery\">\r\n        <div class=\"quick-links\">\r\n          <h3>快速链接</h3>\r\n          <div class=\"links-grid\">\r\n            <router-link to=\"/store\" class=\"quick-link\">访问 Apple Store 在线商店</router-link>\r\n            <router-link to=\"/accessories\" class=\"quick-link\">配件</router-link>\r\n            <router-link to=\"/airpods\" class=\"quick-link\">AirPods</router-link>\r\n            <router-link to=\"/airtag\" class=\"quick-link\">AirTag</router-link>\r\n            <router-link to=\"/apple-tv\" class=\"quick-link\">Apple TV</router-link>\r\n            <router-link to=\"/ipad\" class=\"quick-link\">iPad</router-link>\r\n            <router-link to=\"/iphone\" class=\"quick-link\">iPhone</router-link>\r\n            <router-link to=\"/mac\" class=\"quick-link\">Mac</router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapMutations } from 'vuex'\r\n\r\nexport default {\r\n  name: 'Search',\r\n  data() {\r\n    return {\r\n      searchQuery: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['isSearchOpen'])\r\n  },\r\n  methods: {\r\n    ...mapMutations(['setSearchQuery', 'toggleSearch']),\r\n    handleSearch() {\r\n      this.setSearchQuery(this.searchQuery)\r\n    },\r\n    closeSearch() {\r\n      this.toggleSearch()\r\n      this.searchQuery = ''\r\n      this.setSearchQuery('')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.search-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: saturate(180%) blur(20px);\r\n  z-index: 10000;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-overlay.active {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n.search-container {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.search-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.search-input-wrapper {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 10px 15px;\r\n}\r\n\r\n.search-icon {\r\n  margin-right: 10px;\r\n}\r\n\r\ninput {\r\n  width: 100%;\r\n  background: none;\r\n  border: none;\r\n  color: #fff;\r\n  font-size: 17px;\r\n  outline: none;\r\n}\r\n\r\ninput::placeholder {\r\n  color: #86868b;\r\n}\r\n\r\n.close-button {\r\n  background: none;\r\n  border: none;\r\n  color: #fff;\r\n  font-size: 17px;\r\n  cursor: pointer;\r\n  padding: 10px;\r\n}\r\n\r\n.search-results {\r\n  color: #fff;\r\n}\r\n\r\n.quick-links h3 {\r\n  font-size: 12px;\r\n  color: #86868b;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.links-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.quick-link {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.quick-link:hover {\r\n  text-decoration: underline;\r\n}\r\n</style> "], "mappings": ";;EAESA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAsB;;;EAc9BA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAY;;;uBApB/BC,mBAAA,CAiCM;IAjCDD,KAAK,EAAAE,eAAA,EAAC,gBAAgB;MAAAC,MAAA,EAAmBC,IAAA,CAAAC;IAAY;MACxDC,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJD,mBAAA,CAaM,OAbNE,UAaM,GAZJF,mBAAA,CAUM,OAVNG,UAUM,G,0BATJH,mBAAA,CAEM;IAFDN,KAAK,EAAC,aAAa;IAACU,KAAK,EAAC,4BAA4B;IAACC,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC;MACzFP,mBAAA,CAAub;IAAjbQ,CAAC,EAAC,8ZAA8Z;IAACC,IAAI,EAAC;2CAE9aT,mBAAA,CAKE;IAJAU,IAAI,EAAC,MAAM;+DACFC,KAAA,CAAAC,WAAW,GAAAC,MAAA;IACpBC,WAAW,EAAC,iBAAiB;IAC5BC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,YAAA,IAAAD,QAAA,CAAAC,YAAA,IAAAF,IAAA,CAAY;iEAFXN,KAAA,CAAAC,WAAW,E,KAKxBZ,mBAAA,CAA6D;IAArDN,KAAK,EAAC,cAAc;IAAE0B,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAG,WAAA,IAAAH,QAAA,CAAAG,WAAA,IAAAJ,IAAA,CAAW;KAAE,IAAE,E,GAGpBN,KAAA,CAAAC,WAAW,I,cAA7CjB,mBAAA,CAcM,OAdN2B,UAcM,GAbJtB,mBAAA,CAYM,OAZNuB,UAYM,G,4BAXJvB,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CASM,OATNwB,UASM,GARJC,YAAA,CAA6EC,sBAAA;IAAhEC,EAAE,EAAC,QAAQ;IAACjC,KAAK,EAAC;;sBAAa,MAAmBsB,MAAA,QAAAA,MAAA,O,iBAAnB,qBAAmB,E;;;MAC/DS,YAAA,CAAkEC,sBAAA;IAArDC,EAAE,EAAC,cAAc;IAACjC,KAAK,EAAC;;sBAAa,MAAEsB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;MACpDS,YAAA,CAAmEC,sBAAA;IAAtDC,EAAE,EAAC,UAAU;IAACjC,KAAK,EAAC;;sBAAa,MAAOsB,MAAA,QAAAA,MAAA,O,iBAAP,SAAO,E;;;MACrDS,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC,SAAS;IAACjC,KAAK,EAAC;;sBAAa,MAAMsB,MAAA,QAAAA,MAAA,O,iBAAN,QAAM,E;;;MACnDS,YAAA,CAAqEC,sBAAA;IAAxDC,EAAE,EAAC,WAAW;IAACjC,KAAK,EAAC;;sBAAa,MAAQsB,MAAA,QAAAA,MAAA,O,iBAAR,UAAQ,E;;;MACvDS,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC,OAAO;IAACjC,KAAK,EAAC;;sBAAa,MAAIsB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAC/CS,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC,SAAS;IAACjC,KAAK,EAAC;;sBAAa,MAAMsB,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;;MACnDS,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC,MAAM;IAACjC,KAAK,EAAC;;sBAAa,MAAGsB,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}