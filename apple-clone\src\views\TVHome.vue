<template>
  <div class="tv-home">
    <section class="hero">
      <div class="hero-content">
        <h1>Apple TV</h1>
        <h2>娱乐的未来。</h2>
        <div class="cta-links">
          <router-link to="/tv/apple-tv-4k" class="link">了解更多</router-link>
          <router-link to="/shop/apple-tv" class="link">购买</router-link>
        </div>
      </div>
      <div class="hero-image">
        <img src="https://via.placeholder.com/600x400/000000/FFFFFF?text=Apple+TV" alt="Apple TV" />
      </div>
    </section>

    <section class="product-grid">
      <div class="product-item">
        <h3>Apple TV 4K</h3>
        <p>搭载 A15 仿生芯片</p>
        <div class="cta-links">
          <router-link to="/apple-tv-4k" class="link">了解更多</router-link>
          <router-link to="/shop/apple-tv-4k" class="link">购买</router-link>
        </div>
        <img src="https://via.placeholder.com/400x300/000000/FFFFFF?text=Apple+TV+4K" alt="Apple TV 4K" />
      </div>

      <div class="product-item">
        <h3>Apple TV+</h3>
        <p>原创内容，精彩纷呈</p>
        <div class="cta-links">
          <router-link to="/apple-tv-plus" class="link">了解更多</router-link>
          <router-link to="/shop/apple-tv-plus" class="link">订阅</router-link>
        </div>
        <img src="https://via.placeholder.com/400x300/F5F5F7/000000?text=Apple+TV+" alt="Apple TV+" />
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'TVHome'
}
</script>

<style scoped>
.tv-home {
  padding-top: 44px;
}

.hero {
  background-color: #000;
  color: #fff;
  text-align: center;
  padding: 60px 20px;
}

.hero-content {
  max-width: 1024px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 56px;
  font-weight: 600;
  margin-bottom: 10px;
}

.hero h2 {
  font-size: 28px;
  font-weight: 400;
  margin-bottom: 20px;
}

.cta-links {
  display: flex;
  justify-content: center;
  gap: 35px;
  margin-bottom: 20px;
}

.link {
  color: #2997ff;
  text-decoration: none;
  font-size: 21px;
}

.link:hover {
  text-decoration: underline;
}

.hero-image img {
  max-width: 100%;
  height: auto;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px;
  max-width: 1024px;
  margin: 0 auto;
}

.product-item {
  background-color: #fbfbfd;
  text-align: center;
  padding: 40px 20px;
  border-radius: 18px;
}

.product-item h3 {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: 10px;
}

.product-item p {
  font-size: 21px;
  margin-bottom: 20px;
}

.product-item img {
  max-width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr;
  }

  .hero h1 {
    font-size: 40px;
  }

  .hero h2 {
    font-size: 24px;
  }
}
</style>
