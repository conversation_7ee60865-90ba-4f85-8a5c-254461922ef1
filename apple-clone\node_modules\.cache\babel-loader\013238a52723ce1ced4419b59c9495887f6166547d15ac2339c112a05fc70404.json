{"ast": null, "code": "export default {\n  name: 'iPad'\n};", "map": {"version": 3, "names": ["name"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\iPad.vue"], "sourcesContent": ["<template>\n  <div class=\"ipad\">\n    <section class=\"hero\">\n      <div class=\"hero-content\">\n        <h1>iPad</h1>\n        <h2>强大。多彩。精彩。</h2>\n        <div class=\"cta-links\">\n          <router-link to=\"/ipad/ipad-pro\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/ipad\" class=\"link\">购买</router-link>\n        </div>\n      </div>\n      <div class=\"hero-image\">\n        <img src=\"https://via.placeholder.com/600x400/F5F5F7/000000?text=iPad\" alt=\"iPad\" />\n      </div>\n    </section>\n\n    <section class=\"product-grid\">\n      <div class=\"product-item\">\n        <h3>iPad Pro</h3>\n        <p>搭载 M2 芯片</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/ipad-pro\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/ipad-pro\" class=\"link\">购买</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Pro\" alt=\"iPad Pro\" />\n      </div>\n\n      <div class=\"product-item\">\n        <h3>iPad Air</h3>\n        <p>搭载 M1 芯片</p>\n        <div class=\"cta-links\">\n          <router-link to=\"/ipad-air\" class=\"link\">了解更多</router-link>\n          <router-link to=\"/shop/ipad-air\" class=\"link\">购买</router-link>\n        </div>\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Air\" alt=\"iPad Air\" />\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'iPad'\n}\n</script>\n\n<style scoped>\n.ipad {\n  padding-top: 44px;\n}\n\n.hero {\n  background-color: #f5f5f7;\n  color: #1d1d1f;\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.hero-content {\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.hero h1 {\n  font-size: 56px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.hero h2 {\n  font-size: 28px;\n  font-weight: 400;\n  margin-bottom: 20px;\n}\n\n.cta-links {\n  display: flex;\n  justify-content: center;\n  gap: 35px;\n  margin-bottom: 20px;\n}\n\n.link {\n  color: #2997ff;\n  text-decoration: none;\n  font-size: 21px;\n}\n\n.link:hover {\n  text-decoration: underline;\n}\n\n.hero-image img {\n  max-width: 100%;\n  height: auto;\n}\n\n.product-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20px;\n  padding: 20px;\n  max-width: 1024px;\n  margin: 0 auto;\n}\n\n.product-item {\n  background-color: #fbfbfd;\n  text-align: center;\n  padding: 40px 20px;\n  border-radius: 18px;\n}\n\n.product-item h3 {\n  font-size: 40px;\n  font-weight: 600;\n  margin-bottom: 10px;\n}\n\n.product-item p {\n  font-size: 21px;\n  margin-bottom: 20px;\n}\n\n.product-item img {\n  max-width: 100%;\n  height: auto;\n}\n\n@media (max-width: 768px) {\n  .product-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .hero h1 {\n    font-size: 40px;\n  }\n\n  .hero h2 {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": "AAyCA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}