{"ast": null, "code": "import { products } from '../data/products';\nimport { mapMutations } from 'vuex';\nexport default {\n  name: 'Store',\n  data() {\n    return {\n      products\n    };\n  },\n  methods: {\n    ...mapMutations(['addToCart'])\n  }\n};", "map": {"version": 3, "names": ["products", "mapMutations", "name", "data", "methods"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Store.vue"], "sourcesContent": ["<template>\r\n  <div class=\"store-page\">\r\n    <div class=\"store-container\">\r\n      <h1>Apple Store 在线商店</h1>\r\n      <div class=\"products-grid\">\r\n        <div v-for=\"product in products\" :key=\"product.id\" class=\"product-card\">\r\n          <img :src=\"product.image\" :alt=\"product.name\" class=\"product-image\" />\r\n          <h3>{{ product.name }}</h3>\r\n          <p class=\"product-description\">{{ product.description }}</p>\r\n          <p class=\"product-price\">¥{{ product.price }}</p>\r\n          <button class=\"buy-button\" @click=\"addToCart(product)\">购买</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { products } from '../data/products'\r\nimport { mapMutations } from 'vuex'\r\n\r\nexport default {\r\n  name: 'Store',\r\n  data() {\r\n    return {\r\n      products\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapMutations(['addToCart'])\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.store-page {\r\n  padding-top: 44px;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f7;\r\n}\r\n\r\n.store-container {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n  padding: 40px 20px;\r\n}\r\n\r\nh1 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.products-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 30px;\r\n}\r\n\r\n.product-card {\r\n  background: #fff;\r\n  border-radius: 18px;\r\n  padding: 20px;\r\n  text-align: center;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.product-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.product-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: contain;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.product-card h3 {\r\n  font-size: 24px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.product-description {\r\n  color: #86868b;\r\n  font-size: 17px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.product-price {\r\n  font-size: 21px;\r\n  font-weight: 600;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.buy-button {\r\n  width: 100%;\r\n  background: #0071e3;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 980px;\r\n  padding: 12px 24px;\r\n  font-size: 17px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.buy-button:hover {\r\n  background: #0077ed;\r\n}\r\n</style> "], "mappings": "AAkBA,SAASA,QAAO,QAAS,kBAAiB;AAC1C,SAASC,YAAW,QAAS,MAAK;AAElC,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLH;IACF;EACF,CAAC;EACDI,OAAO,EAAE;IACP,GAAGH,YAAY,CAAC,CAAC,WAAW,CAAC;EAC/B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}