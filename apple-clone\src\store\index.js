import { createStore } from 'vuex'

export default createStore({
  state: {
    cart: [],
    searchQuery: '',
    isSearchOpen: false
  },
  mutations: {
    addToCart(state, product) {
      state.cart.push(product)
    },
    removeFromCart(state, productId) {
      state.cart = state.cart.filter(item => item.id !== productId)
    },
    setSearchQuery(state, query) {
      state.searchQuery = query
    },
    toggleSearch(state) {
      state.isSearchOpen = !state.isSearchOpen
    }
  },
  actions: {
    addToCart({ commit }, product) {
      commit('addToCart', product)
    },
    removeFromCart({ commit }, productId) {
      commit('removeFromCart', productId)
    },
    updateSearch({ commit }, query) {
      commit('setSearchQuery', query)
    },
    toggleSearch({ commit }) {
      commit('toggleSearch')
    }
  },
  getters: {
    cartItemCount: state => state.cart.length,
    cartTotal: state => state.cart.reduce((total, item) => total + item.price, 0)
  }
}) 