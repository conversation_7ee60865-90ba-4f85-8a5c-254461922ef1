{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"animation-demo\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"debug-info\"\n};\nconst _hoisted_3 = {\n  id: \"head\",\n  class: \"titleBox\"\n};\nconst _hoisted_4 = {\n  class: \"sticky-content\",\n  ref: \"headContent\"\n};\nconst _hoisted_5 = {\n  class: \"hero-content\"\n};\nconst _hoisted_6 = {\n  ref: \"headTitle\"\n};\nconst _hoisted_7 = {\n  ref: \"headSubtitle\"\n};\nconst _hoisted_8 = {\n  class: \"cta-links\",\n  ref: \"headLinks\"\n};\nconst _hoisted_9 = {\n  class: \"hero-image\",\n  ref: \"headImage\"\n};\nconst _hoisted_10 = {\n  id: \"intro\",\n  class: \"titleBox\"\n};\nconst _hoisted_11 = {\n  class: \"sticky-content\",\n  ref: \"introContent\"\n};\nconst _hoisted_12 = {\n  class: \"intro-section\"\n};\nconst _hoisted_13 = {\n  ref: \"introTitle\"\n};\nconst _hoisted_14 = {\n  ref: \"introText\"\n};\nconst _hoisted_15 = {\n  class: \"feature-grid\",\n  ref: \"introFeatures\"\n};\nconst _hoisted_16 = {\n  id: \"tech\",\n  class: \"titleBox\"\n};\nconst _hoisted_17 = {\n  class: \"sticky-content\",\n  ref: \"techContent\"\n};\nconst _hoisted_18 = {\n  class: \"tech-section\"\n};\nconst _hoisted_19 = {\n  ref: \"techTitle\"\n};\nconst _hoisted_20 = {\n  class: \"tech-grid\",\n  ref: \"techGrid\"\n};\nconst _hoisted_21 = {\n  id: \"company\",\n  class: \"titleBox\"\n};\nconst _hoisted_22 = {\n  class: \"sticky-content\",\n  ref: \"companyContent\"\n};\nconst _hoisted_23 = {\n  class: \"company-section\"\n};\nconst _hoisted_24 = {\n  ref: \"companyTitle\"\n};\nconst _hoisted_25 = {\n  class: \"product-showcase\",\n  ref: \"companyProducts\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 调试信息 \"), $data.showDebug ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"p\", null, \"滚动进度: \" + _toDisplayString($data.currentScrollRate) + \"%\", 1 /* TEXT */), _createElementVNode(\"p\", null, \"当前分区: \" + _toDisplayString($data.currentSection), 1 /* TEXT */), _createElementVNode(\"p\", null, \"分区进度: \" + _toDisplayString($data.currentSectionRate), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 头部分区 \"), _createElementVNode(\"section\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h1\", _hoisted_6, \"iPhone 15 Pro\", 512 /* NEED_PATCH */), _createElementVNode(\"h2\", _hoisted_7, \"钛金属。超强大。超Pro。\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_8, _cache[1] || (_cache[1] = [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"link\"\n  }, \"了解更多\", -1 /* HOISTED */), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"link\"\n  }, \"购买\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_9, _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n    class: \"phone-mockup\"\n  }, \"📱\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 介绍分区 \"), _createElementVNode(\"section\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"h2\", _hoisted_13, \"创新科技\", 512 /* NEED_PATCH */), _createElementVNode(\"p\", _hoisted_14, \"探索Apple最新的技术突破，体验前所未有的性能与设计。\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_15, _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n    class: \"feature-item\"\n  }, [_createElementVNode(\"div\", {\n    class: \"icon\"\n  }, \"🚀\"), _createElementVNode(\"h3\", null, \"A17 Pro芯片\"), _createElementVNode(\"p\", null, \"业界领先的3纳米工艺\")], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"feature-item\"\n  }, [_createElementVNode(\"div\", {\n    class: \"icon\"\n  }, \"💎\"), _createElementVNode(\"h3\", null, \"钛金属设计\"), _createElementVNode(\"p\", null, \"轻盈坚固，精工打造\")], -1 /* HOISTED */)]), 512 /* NEED_PATCH */)])], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 技术分区 \"), _createElementVNode(\"section\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"h2\", _hoisted_19, \"技术规格\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_20, _cache[4] || (_cache[4] = [_createStaticVNode(\"<div class=\\\"tech-item\\\" data-v-86fb4f86><div class=\\\"icon\\\" data-v-86fb4f86>📺</div><h3 data-v-86fb4f86>显示屏</h3><p data-v-86fb4f86>6.1英寸超视网膜XDR显示屏</p></div><div class=\\\"tech-item\\\" data-v-86fb4f86><div class=\\\"icon\\\" data-v-86fb4f86>📷</div><h3 data-v-86fb4f86>摄像头</h3><p data-v-86fb4f86>4800万像素主摄像头系统</p></div><div class=\\\"tech-item\\\" data-v-86fb4f86><div class=\\\"icon\\\" data-v-86fb4f86>🔋</div><h3 data-v-86fb4f86>电池</h3><p data-v-86fb4f86>长达29小时视频播放</p></div>\", 3)]), 512 /* NEED_PATCH */)])], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 公司分区 \"), _createElementVNode(\"section\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"h2\", _hoisted_24, \"Apple生态\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_25, _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n    class: \"product-item\"\n  }, [_createElementVNode(\"div\", {\n    class: \"product-icon\"\n  }, \"💻\"), _createElementVNode(\"h3\", null, \"MacBook Air\"), _createElementVNode(\"p\", null, \"搭载 M2 芯片\")], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"product-item\"\n  }, [_createElementVNode(\"div\", {\n    class: \"product-icon\"\n  }, \"📱\"), _createElementVNode(\"h3\", null, \"iPad Pro\"), _createElementVNode(\"p\", null, \"搭载 M2 芯片\")], -1 /* HOISTED */)]), 512 /* NEED_PATCH */)])], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 切换调试模式按钮 \"), _createElementVNode(\"button\", {\n    class: \"debug-toggle\",\n    onClick: _cache[0] || (_cache[0] = $event => $data.showDebug = !$data.showDebug)\n  }, _toDisplayString($data.showDebug ? '隐藏' : '显示') + \"调试信息 \", 1 /* TEXT */)]);\n}", "map": {"version": 3, "names": ["class", "id", "ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "$data", "showDebug", "_hoisted_2", "_createElementVNode", "_toDisplayString", "currentScrollRate", "currentSection", "currentSectionRate", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_cache", "href", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "onClick", "$event"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\AnimationDemo.vue"], "sourcesContent": ["<template>\n  <div class=\"animation-demo\">\n    <!-- 调试信息 -->\n    <div class=\"debug-info\" v-if=\"showDebug\">\n      <p>滚动进度: {{ currentScrollRate }}%</p>\n      <p>当前分区: {{ currentSection }}</p>\n      <p>分区进度: {{ currentSectionRate }}</p>\n    </div>\n\n    <!-- 头部分区 -->\n    <section id=\"head\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"headContent\">\n        <div class=\"hero-content\">\n          <h1 ref=\"headTitle\">iPhone 15 Pro</h1>\n          <h2 ref=\"headSubtitle\">钛金属。超强大。超Pro。</h2>\n          <div class=\"cta-links\" ref=\"headLinks\">\n            <a href=\"#\" class=\"link\">了解更多</a>\n            <a href=\"#\" class=\"link\">购买</a>\n          </div>\n        </div>\n        <div class=\"hero-image\" ref=\"headImage\">\n          <div class=\"phone-mockup\">📱</div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 介绍分区 -->\n    <section id=\"intro\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"introContent\">\n        <div class=\"intro-section\">\n          <h2 ref=\"introTitle\">创新科技</h2>\n          <p ref=\"introText\">探索Apple最新的技术突破，体验前所未有的性能与设计。</p>\n          <div class=\"feature-grid\" ref=\"introFeatures\">\n            <div class=\"feature-item\">\n              <div class=\"icon\">🚀</div>\n              <h3>A17 Pro芯片</h3>\n              <p>业界领先的3纳米工艺</p>\n            </div>\n            <div class=\"feature-item\">\n              <div class=\"icon\">💎</div>\n              <h3>钛金属设计</h3>\n              <p>轻盈坚固，精工打造</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 技术分区 -->\n    <section id=\"tech\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"techContent\">\n        <div class=\"tech-section\">\n          <h2 ref=\"techTitle\">技术规格</h2>\n          <div class=\"tech-grid\" ref=\"techGrid\">\n            <div class=\"tech-item\">\n              <div class=\"icon\">📺</div>\n              <h3>显示屏</h3>\n              <p>6.1英寸超视网膜XDR显示屏</p>\n            </div>\n            <div class=\"tech-item\">\n              <div class=\"icon\">📷</div>\n              <h3>摄像头</h3>\n              <p>4800万像素主摄像头系统</p>\n            </div>\n            <div class=\"tech-item\">\n              <div class=\"icon\">🔋</div>\n              <h3>电池</h3>\n              <p>长达29小时视频播放</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 公司分区 -->\n    <section id=\"company\" class=\"titleBox\">\n      <div class=\"sticky-content\" ref=\"companyContent\">\n        <div class=\"company-section\">\n          <h2 ref=\"companyTitle\">Apple生态</h2>\n          <div class=\"product-showcase\" ref=\"companyProducts\">\n            <div class=\"product-item\">\n              <div class=\"product-icon\">💻</div>\n              <h3>MacBook Air</h3>\n              <p>搭载 M2 芯片</p>\n            </div>\n            <div class=\"product-item\">\n              <div class=\"product-icon\">📱</div>\n              <h3>iPad Pro</h3>\n              <p>搭载 M2 芯片</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 切换调试模式按钮 -->\n    <button class=\"debug-toggle\" @click=\"showDebug = !showDebug\">\n      {{ showDebug ? '隐藏' : '显示' }}调试信息\n    </button>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AnimationDemo',\n  data() {\n    return {\n      showDebug: true,\n      currentScrollRate: 0,\n      currentSection: '',\n      currentSectionRate: 0,\n      data: {\n        sections: [],\n        animateMomentInfo: {},\n        scrollHeight: 0,\n        clientHeight: 0\n      }\n    }\n  },\n  mounted() {\n    this.initScrollAnimation()\n  },\n  beforeUnmount() {\n    window.removeEventListener('scroll', this.handleScroll)\n    window.removeEventListener('resize', this.handleResize)\n  },\n  methods: {\n    // 初始化滚动动画\n    initScrollAnimation() {\n      this.data.sections = ['head', 'intro', 'tech', 'company']\n      this.updateScrollData()\n      this.countAnimateMomentInfo()\n      \n      // 绑定滚动事件\n      window.addEventListener('scroll', this.handleScroll)\n      window.addEventListener('resize', this.handleResize)\n      \n      // 初始化动画状态\n      this.handleScroll()\n    },\n\n    // 更新滚动相关数据\n    updateScrollData() {\n      this.data.scrollHeight = document.documentElement.scrollHeight\n      this.data.clientHeight = document.documentElement.clientHeight\n    },\n\n    // 计算每个section的动画触发时机\n    countAnimateMomentInfo() {\n      for (const sectionId of this.data.sections) {\n        const node = document.getElementById(sectionId)\n        if (node) {\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node)\n        }\n      }\n    },\n\n    // 根据section的高度，计算它在页面的位置\n    blockAnimateStart(node) {\n      const begin = this.countScrollRatio(node.offsetTop)\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight)\n      return { begin, end }\n    },\n\n    // 计算当前位置距离顶部高度占整个页面的百分比\n    countScrollRatio(scrollTop) {\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4))\n    },\n\n    // 滚动事件处理\n    handleScroll() {\n      const top = document.documentElement.scrollTop\n      const rate = this.countScrollRatio(top)\n      this.currentScrollRate = rate.toFixed(2)\n      this.activateAnimate(rate)\n    },\n\n    // 窗口大小改变事件处理\n    handleResize() {\n      this.updateScrollData()\n      this.countAnimateMomentInfo()\n    },\n\n    // 激活动画\n    activateAnimate(rate) {\n      let activeSection = ''\n      let activeSectionRate = 0\n      \n      for (let key in this.data.animateMomentInfo) {\n        const { begin, end } = this.data.animateMomentInfo[key]\n        if (rate >= begin && rate <= end) {\n          const progress = ((rate - begin) / (end - begin))\n          const clampedProgress = Math.min(1, Math.max(0, progress))\n          this.executeAnimate(key, clampedProgress)\n          activeSection = key\n          activeSectionRate = (clampedProgress * 100).toFixed(1)\n        }\n      }\n      \n      this.currentSection = activeSection\n      this.currentSectionRate = activeSectionRate\n    },\n\n    // 执行具体的动画\n    executeAnimate(id, rate) {\n      switch (id) {\n        case 'head':\n          this.headAnimate(rate)\n          break\n        case 'intro':\n          this.introAnimate(rate)\n          break\n        case 'tech':\n          this.techAnimate(rate)\n          break\n        case 'company':\n          this.companyAnimate(rate)\n          break\n        default:\n          console.log('no action for', id)\n      }\n    },\n\n    // 头部分区动画\n    headAnimate(rate) {\n      const title = this.$refs.headTitle\n      const subtitle = this.$refs.headSubtitle\n      const links = this.$refs.headLinks\n      const image = this.$refs.headImage\n\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * 50}px)`\n        title.style.opacity = rate\n      }\n      if (subtitle) {\n        subtitle.style.transform = `translateY(${(1 - rate) * 30}px)`\n        subtitle.style.opacity = rate\n      }\n      if (links) {\n        links.style.transform = `translateY(${(1 - rate) * 20}px)`\n        links.style.opacity = rate\n      }\n      if (image) {\n        image.style.transform = `scale(${0.8 + rate * 0.2}) translateX(${(1 - rate) * 100}px)`\n        image.style.opacity = rate\n      }\n    },\n\n    // 介绍分区动画\n    introAnimate(rate) {\n      const title = this.$refs.introTitle\n      const text = this.$refs.introText\n      const features = this.$refs.introFeatures\n\n      if (title) {\n        title.style.transform = `translateX(${(1 - rate) * -100}px)`\n        title.style.opacity = rate\n      }\n      if (text) {\n        text.style.transform = `translateX(${(1 - rate) * -50}px)`\n        text.style.opacity = rate\n      }\n      if (features) {\n        features.style.transform = `translateY(${(1 - rate) * 50}px)`\n        features.style.opacity = rate\n      }\n    },\n\n    // 技术分区动画\n    techAnimate(rate) {\n      const title = this.$refs.techTitle\n      const grid = this.$refs.techGrid\n\n      if (title) {\n        title.style.transform = `scale(${0.5 + rate * 0.5})`\n        title.style.opacity = rate\n      }\n      if (grid) {\n        const items = grid.children\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.2\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))\n          items[i].style.transform = `translateY(${(1 - itemRate) * 30}px) rotateX(${(1 - itemRate) * 15}deg)`\n          items[i].style.opacity = itemRate\n        }\n      }\n    },\n\n    // 公司分区动画\n    companyAnimate(rate) {\n      const title = this.$refs.companyTitle\n      const products = this.$refs.companyProducts\n\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * -30}px)`\n        title.style.opacity = rate\n      }\n      if (products) {\n        const items = products.children\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.3\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))\n          items[i].style.transform = `scale(${0.8 + itemRate * 0.2}) rotateY(${(1 - itemRate) * 10}deg)`\n          items[i].style.opacity = itemRate\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.animation-demo {\n  position: relative;\n}\n\n/* 调试信息 */\n.debug-info {\n  position: fixed;\n  top: 60px;\n  right: 20px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  font-family: monospace;\n  font-size: 14px;\n  z-index: 1000;\n  backdrop-filter: blur(10px);\n}\n\n.debug-info p {\n  margin: 5px 0;\n}\n\n.debug-toggle {\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  background: #007aff;\n  color: white;\n  border: none;\n  padding: 12px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 14px;\n  z-index: 1000;\n  transition: all 0.3s ease;\n}\n\n.debug-toggle:hover {\n  background: #0056cc;\n  transform: scale(1.05);\n}\n\n/* 分区基础样式 */\n.titleBox {\n  height: 100vh;\n  position: relative;\n  overflow: hidden;\n}\n\n.sticky-content {\n  position: sticky;\n  top: 0;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 20px;\n  transition: all 0.1s ease-out;\n}\n\n/* 头部分区样式 */\n#head {\n  background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);\n  color: #fff;\n}\n\n#head .sticky-content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 60px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n#head .hero-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  text-align: left;\n}\n\n#head h1 {\n  font-size: 64px;\n  font-weight: 700;\n  margin-bottom: 20px;\n  line-height: 1.1;\n  opacity: 0;\n  transform: translateY(50px);\n}\n\n#head h2 {\n  font-size: 32px;\n  font-weight: 400;\n  margin-bottom: 40px;\n  color: #a1a1a6;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.cta-links {\n  display: flex;\n  gap: 30px;\n  opacity: 0;\n  transform: translateY(20px);\n}\n\n.link {\n  color: #2997ff;\n  text-decoration: none;\n  font-size: 21px;\n  font-weight: 500;\n  padding: 12px 24px;\n  border: 1px solid #2997ff;\n  border-radius: 25px;\n  transition: all 0.3s ease;\n}\n\n.link:hover {\n  background-color: #2997ff;\n  color: #fff;\n}\n\n.hero-image {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transform: scale(0.8) translateX(100px);\n}\n\n.phone-mockup {\n  font-size: 120px;\n  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.5));\n}\n\n/* 介绍分区样式 */\n#intro {\n  background: linear-gradient(135deg, #f5f5f7 0%, #e8e8ed 100%);\n  color: #1d1d1f;\n}\n\n.intro-section {\n  max-width: 800px;\n  text-align: center;\n}\n\n#intro h2 {\n  font-size: 56px;\n  font-weight: 700;\n  margin-bottom: 30px;\n  opacity: 0;\n  transform: translateX(-100px);\n}\n\n#intro p {\n  font-size: 24px;\n  line-height: 1.6;\n  margin-bottom: 50px;\n  color: #6e6e73;\n  opacity: 0;\n  transform: translateX(-50px);\n}\n\n.feature-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 40px;\n  opacity: 0;\n  transform: translateY(50px);\n}\n\n.feature-item {\n  background: #fff;\n  padding: 30px;\n  border-radius: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.feature-item .icon {\n  font-size: 48px;\n  margin-bottom: 20px;\n}\n\n.feature-item h3 {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 15px;\n  color: #1d1d1f;\n}\n\n.feature-item p {\n  font-size: 16px;\n  color: #6e6e73;\n  margin: 0;\n}\n\n/* 技术分区样式 */\n#tech {\n  background: linear-gradient(135deg, #1d1d1f 0%, #2d2d30 100%);\n  color: #fff;\n}\n\n.tech-section {\n  max-width: 1000px;\n  text-align: center;\n}\n\n#tech h2 {\n  font-size: 56px;\n  font-weight: 700;\n  margin-bottom: 60px;\n  opacity: 0;\n  transform: scale(0.5);\n}\n\n.tech-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 40px;\n}\n\n.tech-item {\n  background: rgba(255, 255, 255, 0.1);\n  padding: 40px 30px;\n  border-radius: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  opacity: 0;\n  transform: translateY(30px) rotateX(15deg);\n  perspective: 1000px;\n  text-align: center;\n}\n\n.tech-item .icon {\n  font-size: 48px;\n  margin-bottom: 20px;\n}\n\n.tech-item h3 {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 15px;\n  color: #fff;\n}\n\n.tech-item p {\n  font-size: 16px;\n  color: #a1a1a6;\n  margin: 0;\n}\n\n/* 公司分区样式 */\n#company {\n  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);\n  color: #fff;\n}\n\n.company-section {\n  max-width: 1000px;\n  text-align: center;\n}\n\n#company h2 {\n  font-size: 56px;\n  font-weight: 700;\n  margin-bottom: 60px;\n  opacity: 0;\n  transform: translateY(-30px);\n}\n\n.product-showcase {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 40px;\n}\n\n.product-item {\n  background: rgba(255, 255, 255, 0.15);\n  padding: 40px;\n  border-radius: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  opacity: 0;\n  transform: scale(0.8) rotateY(10deg);\n  perspective: 1000px;\n  text-align: center;\n}\n\n.product-item .product-icon {\n  font-size: 64px;\n  margin-bottom: 20px;\n}\n\n.product-item h3 {\n  font-size: 28px;\n  font-weight: 600;\n  margin-bottom: 15px;\n  color: #fff;\n}\n\n.product-item p {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.8);\n  margin: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  #head .sticky-content {\n    grid-template-columns: 1fr;\n    text-align: center;\n  }\n\n  .tech-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .product-showcase {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  #head h1 {\n    font-size: 48px;\n  }\n\n  #head h2 {\n    font-size: 24px;\n  }\n\n  #intro h2, #tech h2, #company h2 {\n    font-size: 40px;\n  }\n\n  .feature-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .cta-links {\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .debug-info {\n    top: 10px;\n    right: 10px;\n    font-size: 12px;\n    padding: 10px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;;EAEpBA,KAAK,EAAC;;;EAOFC,EAAE,EAAC,MAAM;EAACD,KAAK,EAAC;;;EAClBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAc;;EACnBE,GAAG,EAAC;AAAW;;EACfA,GAAG,EAAC;AAAc;;EACjBF,KAAK,EAAC,WAAW;EAACE,GAAG,EAAC;;;EAKxBF,KAAK,EAAC,YAAY;EAACE,GAAG,EAAC;;;EAOvBD,EAAE,EAAC,OAAO;EAACD,KAAK,EAAC;;;EACnBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAe;;EACpBE,GAAG,EAAC;AAAY;;EACjBA,GAAG,EAAC;AAAW;;EACbF,KAAK,EAAC,cAAc;EAACE,GAAG,EAAC;;;EAiB3BD,EAAE,EAAC,MAAM;EAACD,KAAK,EAAC;;;EAClBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAc;;EACnBE,GAAG,EAAC;AAAW;;EACdF,KAAK,EAAC,WAAW;EAACE,GAAG,EAAC;;;EAsBxBD,EAAE,EAAC,SAAS;EAACD,KAAK,EAAC;;;EACrBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAiB;;EACtBE,GAAG,EAAC;AAAc;;EACjBF,KAAK,EAAC,kBAAkB;EAACE,GAAG,EAAC;;;uBA9E1CC,mBAAA,CAkGM,OAlGNC,UAkGM,GAjGJC,mBAAA,UAAa,EACiBC,KAAA,CAAAC,SAAS,I,cAAvCJ,mBAAA,CAIM,OAJNK,UAIM,GAHJC,mBAAA,CAAqC,WAAlC,QAAM,GAAAC,gBAAA,CAAGJ,KAAA,CAAAK,iBAAiB,IAAG,GAAC,iBACjCF,mBAAA,CAAiC,WAA9B,QAAM,GAAAC,gBAAA,CAAGJ,KAAA,CAAAM,cAAc,kBAC1BH,mBAAA,CAAqC,WAAlC,QAAM,GAAAC,gBAAA,CAAGJ,KAAA,CAAAO,kBAAkB,iB,wCAGhCR,mBAAA,UAAa,EACbI,mBAAA,CAcU,WAdVK,UAcU,GAbRL,mBAAA,CAYM,OAZNM,UAYM,GAXJN,mBAAA,CAOM,OAPNO,UAOM,GANJP,mBAAA,CAAsC,MAAtCQ,UAAsC,EAAlB,eAAa,yBACjCR,mBAAA,CAAyC,MAAzCS,UAAyC,EAAlB,eAAa,yBACpCT,mBAAA,CAGM,OAHNU,UAGM,EAAAC,MAAA,QAAAA,MAAA,OAFJX,mBAAA,CAAiC;IAA9BY,IAAI,EAAC,GAAG;IAACrB,KAAK,EAAC;KAAO,MAAI,qBAC7BS,mBAAA,CAA+B;IAA5BY,IAAI,EAAC,GAAG;IAACrB,KAAK,EAAC;KAAO,IAAE,oB,4BAG/BS,mBAAA,CAEM,OAFNa,UAEM,EAAAF,MAAA,QAAAA,MAAA,OADJX,mBAAA,CAAkC;IAA7BT,KAAK,EAAC;EAAc,GAAC,IAAE,oB,oDAKlCK,mBAAA,UAAa,EACbI,mBAAA,CAmBU,WAnBVc,WAmBU,GAlBRd,mBAAA,CAiBM,OAjBNe,WAiBM,GAhBJf,mBAAA,CAeM,OAfNgB,WAeM,GAdJhB,mBAAA,CAA8B,MAA9BiB,WAA8B,EAAT,MAAI,yBACzBjB,mBAAA,CAAmD,KAAnDkB,WAAmD,EAAhC,8BAA4B,yBAC/ClB,mBAAA,CAWM,OAXNmB,WAWM,EAAAR,MAAA,QAAAA,MAAA,OAVJX,mBAAA,CAIM;IAJDT,KAAK,EAAC;EAAc,IACvBS,mBAAA,CAA0B;IAArBT,KAAK,EAAC;EAAM,GAAC,IAAE,GACpBS,mBAAA,CAAkB,YAAd,WAAS,GACbA,mBAAA,CAAiB,WAAd,YAAU,E,qBAEfA,mBAAA,CAIM;IAJDT,KAAK,EAAC;EAAc,IACvBS,mBAAA,CAA0B;IAArBT,KAAK,EAAC;EAAM,GAAC,IAAE,GACpBS,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAgB,WAAb,WAAS,E,0EAOtBJ,mBAAA,UAAa,EACbI,mBAAA,CAuBU,WAvBVoB,WAuBU,GAtBRpB,mBAAA,CAqBM,OArBNqB,WAqBM,GApBJrB,mBAAA,CAmBM,OAnBNsB,WAmBM,GAlBJtB,mBAAA,CAA6B,MAA7BuB,WAA6B,EAAT,MAAI,yBACxBvB,mBAAA,CAgBM,OAhBNwB,WAgBM,EAAAb,MAAA,QAAAA,MAAA,O,+hBAKZf,mBAAA,UAAa,EACbI,mBAAA,CAkBU,WAlBVyB,WAkBU,GAjBRzB,mBAAA,CAgBM,OAhBN0B,WAgBM,GAfJ1B,mBAAA,CAcM,OAdN2B,WAcM,GAbJ3B,mBAAA,CAAmC,MAAnC4B,WAAmC,EAAZ,SAAO,yBAC9B5B,mBAAA,CAWM,OAXN6B,WAWM,EAAAlB,MAAA,QAAAA,MAAA,OAVJX,mBAAA,CAIM;IAJDT,KAAK,EAAC;EAAc,IACvBS,mBAAA,CAAkC;IAA7BT,KAAK,EAAC;EAAc,GAAC,IAAE,GAC5BS,mBAAA,CAAoB,YAAhB,aAAW,GACfA,mBAAA,CAAe,WAAZ,UAAQ,E,qBAEbA,mBAAA,CAIM;IAJDT,KAAK,EAAC;EAAc,IACvBS,mBAAA,CAAkC;IAA7BT,KAAK,EAAC;EAAc,GAAC,IAAE,GAC5BS,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAe,WAAZ,UAAQ,E,0EAOrBJ,mBAAA,cAAiB,EACjBI,mBAAA,CAES;IAFDT,KAAK,EAAC,cAAc;IAAEuC,OAAK,EAAAnB,MAAA,QAAAA,MAAA,MAAAoB,MAAA,IAAElC,KAAA,CAAAC,SAAS,IAAID,KAAA,CAAAC,SAAS;sBACtDD,KAAA,CAAAC,SAAS,kBAAiB,OAC/B,gB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}