{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home\"\n};\nconst _hoisted_2 = {\n  id: \"head\",\n  class: \"titleBox\"\n};\nconst _hoisted_3 = {\n  class: \"sticky-content\",\n  ref: \"headContent\"\n};\nconst _hoisted_4 = {\n  class: \"hero-content\"\n};\nconst _hoisted_5 = {\n  ref: \"headTitle\"\n};\nconst _hoisted_6 = {\n  ref: \"headSubtitle\"\n};\nconst _hoisted_7 = {\n  class: \"cta-links\",\n  ref: \"headLinks\"\n};\nconst _hoisted_8 = {\n  class: \"hero-image\",\n  ref: \"headImage\"\n};\nconst _hoisted_9 = {\n  id: \"intro\",\n  class: \"titleBox\"\n};\nconst _hoisted_10 = {\n  class: \"sticky-content\",\n  ref: \"introContent\"\n};\nconst _hoisted_11 = {\n  class: \"intro-section\"\n};\nconst _hoisted_12 = {\n  ref: \"introTitle\"\n};\nconst _hoisted_13 = {\n  ref: \"introText\"\n};\nconst _hoisted_14 = {\n  class: \"feature-grid\",\n  ref: \"introFeatures\"\n};\nconst _hoisted_15 = {\n  id: \"tech\",\n  class: \"titleBox\"\n};\nconst _hoisted_16 = {\n  class: \"sticky-content\",\n  ref: \"techContent\"\n};\nconst _hoisted_17 = {\n  class: \"tech-section\"\n};\nconst _hoisted_18 = {\n  ref: \"techTitle\"\n};\nconst _hoisted_19 = {\n  class: \"tech-grid\",\n  ref: \"techGrid\"\n};\nconst _hoisted_20 = {\n  id: \"company\",\n  class: \"titleBox\"\n};\nconst _hoisted_21 = {\n  class: \"sticky-content\",\n  ref: \"companyContent\"\n};\nconst _hoisted_22 = {\n  class: \"company-section\"\n};\nconst _hoisted_23 = {\n  ref: \"companyTitle\"\n};\nconst _hoisted_24 = {\n  class: \"product-showcase\",\n  ref: \"companyProducts\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 头部分区 \"), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"h1\", _hoisted_5, \"iPhone 15 Pro\", 512 /* NEED_PATCH */), _createElementVNode(\"h2\", _hoisted_6, \"钛金属。超强大。超Pro。\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_router_link, {\n    to: \"/iphone-15-pro\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"了解更多\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }), _createVNode(_component_router_link, {\n    to: \"/shop/iphone-15-pro\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"购买\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  })], 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_8, _cache[2] || (_cache[2] = [_createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone+15+Pro\",\n    alt: \"iPhone 15 Pro\"\n  }, null, -1 /* HOISTED */)]), 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 介绍分区 \"), _createElementVNode(\"section\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"h2\", _hoisted_12, \"创新科技\", 512 /* NEED_PATCH */), _createElementVNode(\"p\", _hoisted_13, \"探索Apple最新的技术突破，体验前所未有的性能与设计。\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_14, _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n    class: \"feature-item\"\n  }, [_createElementVNode(\"h3\", null, \"A17 Pro芯片\"), _createElementVNode(\"p\", null, \"业界领先的3纳米工艺\")], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"feature-item\"\n  }, [_createElementVNode(\"h3\", null, \"钛金属设计\"), _createElementVNode(\"p\", null, \"轻盈坚固，精工打造\")], -1 /* HOISTED */)]), 512 /* NEED_PATCH */)])], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 技术分区 \"), _createElementVNode(\"section\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"h2\", _hoisted_18, \"技术规格\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_19, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n    class: \"tech-item\"\n  }, [_createElementVNode(\"h3\", null, \"显示屏\"), _createElementVNode(\"p\", null, \"6.1英寸超视网膜XDR显示屏\")], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"tech-item\"\n  }, [_createElementVNode(\"h3\", null, \"摄像头\"), _createElementVNode(\"p\", null, \"4800万像素主摄像头系统\")], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"tech-item\"\n  }, [_createElementVNode(\"h3\", null, \"电池\"), _createElementVNode(\"p\", null, \"长达29小时视频播放\")], -1 /* HOISTED */)]), 512 /* NEED_PATCH */)])], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 公司分区 \"), _createElementVNode(\"section\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"h2\", _hoisted_23, \"Apple生态\", 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_24, _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n    class: \"product-item\"\n  }, [_createElementVNode(\"h3\", null, \"MacBook Air\"), _createElementVNode(\"p\", null, \"搭载 M2 芯片\"), _createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/400x300/F5F5F7/000000?text=MacBook+Air\",\n    alt: \"MacBook Air\"\n  })], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"product-item\"\n  }, [_createElementVNode(\"h3\", null, \"iPad Pro\"), _createElementVNode(\"p\", null, \"搭载 M2 芯片\"), _createElementVNode(\"img\", {\n    src: \"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Pro\",\n    alt: \"iPad Pro\"\n  })], -1 /* HOISTED */)]), 512 /* NEED_PATCH */)])], 512 /* NEED_PATCH */)])]);\n}", "map": {"version": 3, "names": ["class", "id", "ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_createVNode", "_component_router_link", "to", "_cache", "_hoisted_8", "src", "alt", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <!-- 头部分区 -->\r\n    <section id=\"head\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"headContent\">\r\n        <div class=\"hero-content\">\r\n          <h1 ref=\"headTitle\">iPhone 15 Pro</h1>\r\n          <h2 ref=\"headSubtitle\">钛金属。超强大。超Pro。</h2>\r\n          <div class=\"cta-links\" ref=\"headLinks\">\r\n            <router-link to=\"/iphone-15-pro\" class=\"link\">了解更多</router-link>\r\n            <router-link to=\"/shop/iphone-15-pro\" class=\"link\">购买</router-link>\r\n          </div>\r\n        </div>\r\n        <div class=\"hero-image\" ref=\"headImage\">\r\n          <img src=\"https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone+15+Pro\" alt=\"iPhone 15 Pro\" />\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 介绍分区 -->\r\n    <section id=\"intro\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"introContent\">\r\n        <div class=\"intro-section\">\r\n          <h2 ref=\"introTitle\">创新科技</h2>\r\n          <p ref=\"introText\">探索Apple最新的技术突破，体验前所未有的性能与设计。</p>\r\n          <div class=\"feature-grid\" ref=\"introFeatures\">\r\n            <div class=\"feature-item\">\r\n              <h3>A17 Pro芯片</h3>\r\n              <p>业界领先的3纳米工艺</p>\r\n            </div>\r\n            <div class=\"feature-item\">\r\n              <h3>钛金属设计</h3>\r\n              <p>轻盈坚固，精工打造</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 技术分区 -->\r\n    <section id=\"tech\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"techContent\">\r\n        <div class=\"tech-section\">\r\n          <h2 ref=\"techTitle\">技术规格</h2>\r\n          <div class=\"tech-grid\" ref=\"techGrid\">\r\n            <div class=\"tech-item\">\r\n              <h3>显示屏</h3>\r\n              <p>6.1英寸超视网膜XDR显示屏</p>\r\n            </div>\r\n            <div class=\"tech-item\">\r\n              <h3>摄像头</h3>\r\n              <p>4800万像素主摄像头系统</p>\r\n            </div>\r\n            <div class=\"tech-item\">\r\n              <h3>电池</h3>\r\n              <p>长达29小时视频播放</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 公司分区 -->\r\n    <section id=\"company\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"companyContent\">\r\n        <div class=\"company-section\">\r\n          <h2 ref=\"companyTitle\">Apple生态</h2>\r\n          <div class=\"product-showcase\" ref=\"companyProducts\">\r\n            <div class=\"product-item\">\r\n              <h3>MacBook Air</h3>\r\n              <p>搭载 M2 芯片</p>\r\n              <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=MacBook+Air\" alt=\"MacBook Air\" />\r\n            </div>\r\n            <div class=\"product-item\">\r\n              <h3>iPad Pro</h3>\r\n              <p>搭载 M2 芯片</p>\r\n              <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Pro\" alt=\"iPad Pro\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      data: {\r\n        sections: [],\r\n        animateMomentInfo: {},\r\n        scrollHeight: 0,\r\n        clientHeight: 0\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initScrollAnimation()\r\n  },\r\n  beforeUnmount() {\r\n    window.removeEventListener('scroll', this.handleScroll)\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  methods: {\r\n    // 初始化滚动动画\r\n    initScrollAnimation() {\r\n      this.data.sections = ['head', 'intro', 'tech', 'company']\r\n      this.updateScrollData()\r\n      this.countAnimateMomentInfo()\r\n\r\n      // 绑定滚动事件\r\n      window.addEventListener('scroll', this.handleScroll)\r\n      window.addEventListener('resize', this.handleResize)\r\n\r\n      // 初始化动画状态\r\n      this.handleScroll()\r\n    },\r\n\r\n    // 更新滚动相关数据\r\n    updateScrollData() {\r\n      this.data.scrollHeight = document.documentElement.scrollHeight\r\n      this.data.clientHeight = document.documentElement.clientHeight\r\n    },\r\n\r\n    // 计算每个section的动画触发时机\r\n    countAnimateMomentInfo() {\r\n      for (const sectionId of this.data.sections) {\r\n        const node = document.getElementById(sectionId)\r\n        if (node) {\r\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 根据section的高度，计算它在页面的位置\r\n    blockAnimateStart(node) {\r\n      const begin = this.countScrollRatio(node.offsetTop)\r\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight)\r\n      return { begin, end }\r\n    },\r\n\r\n    // 计算当前位置距离顶部高度占整个页面的百分比\r\n    countScrollRatio(scrollTop) {\r\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4))\r\n    },\r\n\r\n    // 滚动事件处理\r\n    handleScroll() {\r\n      const top = document.documentElement.scrollTop\r\n      this.activateAnimate(this.countScrollRatio(top))\r\n    },\r\n\r\n    // 窗口大小改变事件处理\r\n    handleResize() {\r\n      this.updateScrollData()\r\n      this.countAnimateMomentInfo()\r\n    },\r\n\r\n    // 激活动画\r\n    activateAnimate(rate) {\r\n      for (let key in this.data.animateMomentInfo) {\r\n        const { begin, end } = this.data.animateMomentInfo[key]\r\n        if (rate >= begin && rate <= end) {\r\n          const progress = ((rate - begin) / (end - begin)).toFixed(3)\r\n          this.executeAnimate(key, Math.min(1, Math.max(0, progress)))\r\n        }\r\n      }\r\n    },\r\n\r\n    // 执行具体的动画\r\n    executeAnimate(id, rate) {\r\n      switch (id) {\r\n        case 'head':\r\n          this.headAnimate(rate)\r\n          break\r\n        case 'intro':\r\n          this.introAnimate(rate)\r\n          break\r\n        case 'tech':\r\n          this.techAnimate(rate)\r\n          break\r\n        case 'company':\r\n          this.companyAnimate(rate)\r\n          break\r\n        default:\r\n          console.log('no action for', id)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding-top: 44px;\r\n}\r\n\r\n.hero {\r\n  background-color: #000;\r\n  color: #fff;\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n}\r\n\r\n.hero-content {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.hero h1 {\r\n  font-size: 56px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.hero h2 {\r\n  font-size: 28px;\r\n  font-weight: 400;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.cta-links {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 35px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.link {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 21px;\r\n}\r\n\r\n.link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.hero-image img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n.product-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  padding: 20px;\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.product-item {\r\n  background-color: #fbfbfd;\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  border-radius: 18px;\r\n}\r\n\r\n.product-item h3 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.product-item p {\r\n  font-size: 21px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.product-item img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .product-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .hero h1 {\r\n    font-size: 40px;\r\n  }\r\n\r\n  .hero h2 {\r\n    font-size: 24px;\r\n  }\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAM;;EAENC,EAAE,EAAC,MAAM;EAACD,KAAK,EAAC;;;EAClBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAc;;EACnBE,GAAG,EAAC;AAAW;;EACfA,GAAG,EAAC;AAAc;;EACjBF,KAAK,EAAC,WAAW;EAACE,GAAG,EAAC;;;EAKxBF,KAAK,EAAC,YAAY;EAACE,GAAG,EAAC;;;EAOvBD,EAAE,EAAC,OAAO;EAACD,KAAK,EAAC;;;EACnBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAe;;EACpBE,GAAG,EAAC;AAAY;;EACjBA,GAAG,EAAC;AAAW;;EACbF,KAAK,EAAC,cAAc;EAACE,GAAG,EAAC;;;EAe3BD,EAAE,EAAC,MAAM;EAACD,KAAK,EAAC;;;EAClBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAc;;EACnBE,GAAG,EAAC;AAAW;;EACdF,KAAK,EAAC,WAAW;EAACE,GAAG,EAAC;;;EAmBxBD,EAAE,EAAC,SAAS;EAACD,KAAK,EAAC;;;EACrBA,KAAK,EAAC,gBAAgB;EAACE,GAAG,EAAC;;;EACzBF,KAAK,EAAC;AAAiB;;EACtBE,GAAG,EAAC;AAAc;;EACjBF,KAAK,EAAC,kBAAkB;EAACE,GAAG,EAAC;;;;uBAlE1CC,mBAAA,CAiFM,OAjFNC,UAiFM,GAhFJC,mBAAA,UAAa,EACbC,mBAAA,CAcU,WAdVC,UAcU,GAbRD,mBAAA,CAYM,OAZNE,UAYM,GAXJF,mBAAA,CAOM,OAPNG,UAOM,GANJH,mBAAA,CAAsC,MAAtCI,UAAsC,EAAlB,eAAa,yBACjCJ,mBAAA,CAAyC,MAAzCK,UAAyC,EAAlB,eAAa,yBACpCL,mBAAA,CAGM,OAHNM,UAGM,GAFJC,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC,gBAAgB;IAACf,KAAK,EAAC;;sBAAO,MAAIgB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAClDH,YAAA,CAAmEC,sBAAA;IAAtDC,EAAE,EAAC,qBAAqB;IAACf,KAAK,EAAC;;sBAAO,MAAEgB,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;gCAGzDV,mBAAA,CAEM,OAFNW,UAEM,EAAAD,MAAA,QAAAA,MAAA,OADJV,mBAAA,CAAsG;IAAjGY,GAAG,EAAC,sEAAsE;IAACC,GAAG,EAAC;iFAK1Fd,mBAAA,UAAa,EACbC,mBAAA,CAiBU,WAjBVc,UAiBU,GAhBRd,mBAAA,CAeM,OAfNe,WAeM,GAdJf,mBAAA,CAaM,OAbNgB,WAaM,GAZJhB,mBAAA,CAA8B,MAA9BiB,WAA8B,EAAT,MAAI,yBACzBjB,mBAAA,CAAmD,KAAnDkB,WAAmD,EAAhC,8BAA4B,yBAC/ClB,mBAAA,CASM,OATNmB,WASM,EAAAT,MAAA,QAAAA,MAAA,OARJV,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAc,IACvBM,mBAAA,CAAkB,YAAd,WAAS,GACbA,mBAAA,CAAiB,WAAd,YAAU,E,qBAEfA,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAc,IACvBM,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAgB,WAAb,WAAS,E,0EAOtBD,mBAAA,UAAa,EACbC,mBAAA,CAoBU,WApBVoB,WAoBU,GAnBRpB,mBAAA,CAkBM,OAlBNqB,WAkBM,GAjBJrB,mBAAA,CAgBM,OAhBNsB,WAgBM,GAfJtB,mBAAA,CAA6B,MAA7BuB,WAA6B,EAAT,MAAI,yBACxBvB,mBAAA,CAaM,OAbNwB,WAaM,EAAAd,MAAA,QAAAA,MAAA,OAZJV,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAW,IACpBM,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAsB,WAAnB,iBAAe,E,qBAEpBA,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAW,IACpBM,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAoB,WAAjB,eAAa,E,qBAElBA,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAW,IACpBM,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAiB,WAAd,YAAU,E,0EAOvBD,mBAAA,UAAa,EACbC,mBAAA,CAkBU,WAlBVyB,WAkBU,GAjBRzB,mBAAA,CAgBM,OAhBN0B,WAgBM,GAfJ1B,mBAAA,CAcM,OAdN2B,WAcM,GAbJ3B,mBAAA,CAAmC,MAAnC4B,WAAmC,EAAZ,SAAO,yBAC9B5B,mBAAA,CAWM,OAXN6B,WAWM,EAAAnB,MAAA,QAAAA,MAAA,OAVJV,mBAAA,CAIM;IAJDN,KAAK,EAAC;EAAc,IACvBM,mBAAA,CAAoB,YAAhB,aAAW,GACfA,mBAAA,CAAe,WAAZ,UAAQ,GACXA,mBAAA,CAAkG;IAA7FY,GAAG,EAAC,oEAAoE;IAACC,GAAG,EAAC;0BAEpFb,mBAAA,CAIM;IAJDN,KAAK,EAAC;EAAc,IACvBM,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAe,WAAZ,UAAQ,GACXA,mBAAA,CAA4F;IAAvFY,GAAG,EAAC,iEAAiE;IAACC,GAAG,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}