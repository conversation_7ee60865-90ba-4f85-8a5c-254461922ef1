<template>
  <div class="iphone">
    <section class="hero">
      <div class="hero-content">
        <h1>iPhone</h1>
        <h2>强大。美丽。持久。</h2>
        <div class="cta-links">
          <router-link to="/iphone/iphone-15" class="link">了解更多</router-link>
          <router-link to="/shop/iphone" class="link">购买</router-link>
        </div>
      </div>
      <div class="hero-image">
        <img src="https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone" alt="iPhone" />
      </div>
    </section>

    <section class="product-grid">
      <div class="product-item">
        <h3>iPhone 15 Pro</h3>
        <p>钛金属。超强大。超Pro。</p>
        <div class="cta-links">
          <router-link to="/iphone-15-pro" class="link">了解更多</router-link>
          <router-link to="/shop/iphone-15-pro" class="link">购买</router-link>
        </div>
        <img src="https://via.placeholder.com/400x300/000000/FFFFFF?text=iPhone+15+Pro" alt="iPhone 15 Pro" />
      </div>

      <div class="product-item">
        <h3>iPhone 15</h3>
        <p>新一代iPhone</p>
        <div class="cta-links">
          <router-link to="/iphone-15" class="link">了解更多</router-link>
          <router-link to="/shop/iphone-15" class="link">购买</router-link>
        </div>
        <img src="https://via.placeholder.com/400x300/F5F5F7/000000?text=iPhone+15" alt="iPhone 15" />
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'iPhone'
}
</script>

<style scoped>
.iphone {
  padding-top: 44px;
}

.hero {
  background-color: #000;
  color: #fff;
  text-align: center;
  padding: 60px 20px;
}

.hero-content {
  max-width: 1024px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 56px;
  font-weight: 600;
  margin-bottom: 10px;
}

.hero h2 {
  font-size: 28px;
  font-weight: 400;
  margin-bottom: 20px;
}

.cta-links {
  display: flex;
  justify-content: center;
  gap: 35px;
  margin-bottom: 20px;
}

.link {
  color: #2997ff;
  text-decoration: none;
  font-size: 21px;
}

.link:hover {
  text-decoration: underline;
}

.hero-image img {
  max-width: 100%;
  height: auto;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px;
  max-width: 1024px;
  margin: 0 auto;
}

.product-item {
  background-color: #fbfbfd;
  text-align: center;
  padding: 40px 20px;
  border-radius: 18px;
}

.product-item h3 {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: 10px;
}

.product-item p {
  font-size: 21px;
  margin-bottom: 20px;
}

.product-item img {
  max-width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr;
  }

  .hero h1 {
    font-size: 40px;
  }

  .hero h2 {
    font-size: 24px;
  }
}
</style>
