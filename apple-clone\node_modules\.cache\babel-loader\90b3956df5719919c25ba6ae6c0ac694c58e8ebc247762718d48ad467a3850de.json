{"ast": null, "code": "export default {\n  name: 'Home'\n};", "map": {"version": 3, "names": ["name"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <section class=\"hero\">\r\n      <div class=\"hero-content\">\r\n        <h1>iPhone 15 Pro</h1>\r\n        <h2>钛金属。超强大。超Pro。</h2>\r\n        <div class=\"cta-links\">\r\n          <router-link to=\"/iphone-15-pro\" class=\"link\">了解更多</router-link>\r\n          <router-link to=\"/shop/iphone-15-pro\" class=\"link\">购买</router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"hero-image\">\r\n        <img src=\"https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone+15+Pro\" alt=\"iPhone 15 Pro\" />\r\n      </div>\r\n    </section>\r\n\r\n    <section class=\"product-grid\">\r\n      <div class=\"product-item\">\r\n        <h3>MacBook Air</h3>\r\n        <p>搭载 M2 芯片</p>\r\n        <div class=\"cta-links\">\r\n          <router-link to=\"/macbook-air\" class=\"link\">了解更多</router-link>\r\n          <router-link to=\"/shop/macbook-air\" class=\"link\">购买</router-link>\r\n        </div>\r\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=MacBook+Air\" alt=\"MacBook Air\" />\r\n      </div>\r\n\r\n      <div class=\"product-item\">\r\n        <h3>iPad Pro</h3>\r\n        <p>搭载 M2 芯片</p>\r\n        <div class=\"cta-links\">\r\n          <router-link to=\"/ipad-pro\" class=\"link\">了解更多</router-link>\r\n          <router-link to=\"/shop/ipad-pro\" class=\"link\">购买</router-link>\r\n        </div>\r\n        <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Pro\" alt=\"iPad Pro\" />\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Home'\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding-top: 44px;\r\n}\r\n\r\n.hero {\r\n  background-color: #000;\r\n  color: #fff;\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n}\r\n\r\n.hero-content {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.hero h1 {\r\n  font-size: 56px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.hero h2 {\r\n  font-size: 28px;\r\n  font-weight: 400;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.cta-links {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 35px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.link {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 21px;\r\n}\r\n\r\n.link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.hero-image img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n.product-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  padding: 20px;\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.product-item {\r\n  background-color: #fbfbfd;\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  border-radius: 18px;\r\n}\r\n\r\n.product-item h3 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.product-item p {\r\n  font-size: 21px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.product-item img {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .product-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .hero h1 {\r\n    font-size: 40px;\r\n  }\r\n\r\n  .hero h2 {\r\n    font-size: 24px;\r\n  }\r\n}\r\n</style> "], "mappings": "AAyCA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}