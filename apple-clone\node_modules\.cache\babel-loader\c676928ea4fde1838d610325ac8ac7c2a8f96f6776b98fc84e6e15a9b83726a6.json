{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bag-page\"\n};\nconst _hoisted_2 = {\n  class: \"bag-container\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"empty-bag\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"bag-content\"\n};\nconst _hoisted_5 = {\n  class: \"cart-items\"\n};\nconst _hoisted_6 = [\"src\", \"alt\"];\nconst _hoisted_7 = {\n  class: \"item-details\"\n};\nconst _hoisted_8 = {\n  class: \"item-price\"\n};\nconst _hoisted_9 = {\n  class: \"item-actions\"\n};\nconst _hoisted_10 = [\"onClick\"];\nconst _hoisted_11 = {\n  class: \"order-summary\"\n};\nconst _hoisted_12 = {\n  class: \"summary-row\"\n};\nconst _hoisted_13 = {\n  class: \"summary-row total\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[7] || (_cache[7] = _createElementVNode(\"h1\", null, \"购物袋\", -1 /* HOISTED */)), _ctx.cart.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"您的购物袋是空的。\", -1 /* HOISTED */)), _createVNode(_component_router_link, {\n    to: \"/store\",\n    class: \"continue-shopping\"\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"继续购物\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.cart, item => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: item.id,\n      class: \"cart-item\"\n    }, [_createElementVNode(\"img\", {\n      src: item.image,\n      alt: item.name,\n      class: \"item-image\"\n    }, null, 8 /* PROPS */, _hoisted_6), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h3\", null, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_8, \"¥\" + _toDisplayString(item.price), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"button\", {\n      class: \"remove-button\",\n      onClick: $event => _ctx.removeFromCart(item.id)\n    }, \"移除\", 8 /* PROPS */, _hoisted_10)])])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_11, [_cache[4] || (_cache[4] = _createElementVNode(\"h2\", null, \"订单摘要\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_cache[2] || (_cache[2] = _createElementVNode(\"span\", null, \"小计\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"¥\" + _toDisplayString(_ctx.cartTotal), 1 /* TEXT */)]), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"summary-row\"\n  }, [_createElementVNode(\"span\", null, \"运费\"), _createElementVNode(\"span\", null, \"免费\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, [_cache[3] || (_cache[3] = _createElementVNode(\"span\", null, \"总计\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"¥\" + _toDisplayString(_ctx.cartTotal), 1 /* TEXT */)]), _cache[6] || (_cache[6] = _createElementVNode(\"button\", {\n    class: \"checkout-button\"\n  }, \"结账\", -1 /* HOISTED */))])]))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_ctx", "cart", "length", "_hoisted_3", "_createVNode", "_component_router_link", "to", "_cache", "_hoisted_4", "_hoisted_5", "_Fragment", "_renderList", "item", "key", "id", "src", "image", "alt", "name", "_hoisted_7", "_toDisplayString", "_hoisted_8", "price", "_hoisted_9", "onClick", "$event", "removeFromCart", "_hoisted_10", "_hoisted_11", "_hoisted_12", "cartTotal", "_hoisted_13"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Bag.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bag-page\">\r\n    <div class=\"bag-container\">\r\n      <h1>购物袋</h1>\r\n      \r\n      <div v-if=\"cart.length === 0\" class=\"empty-bag\">\r\n        <p>您的购物袋是空的。</p>\r\n        <router-link to=\"/store\" class=\"continue-shopping\">继续购物</router-link>\r\n      </div>\r\n\r\n      <div v-else class=\"bag-content\">\r\n        <div class=\"cart-items\">\r\n          <div v-for=\"item in cart\" :key=\"item.id\" class=\"cart-item\">\r\n            <img :src=\"item.image\" :alt=\"item.name\" class=\"item-image\" />\r\n            <div class=\"item-details\">\r\n              <h3>{{ item.name }}</h3>\r\n              <p class=\"item-price\">¥{{ item.price }}</p>\r\n              <div class=\"item-actions\">\r\n                <button class=\"remove-button\" @click=\"removeFromCart(item.id)\">移除</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"order-summary\">\r\n          <h2>订单摘要</h2>\r\n          <div class=\"summary-row\">\r\n            <span>小计</span>\r\n            <span>¥{{ cartTotal }}</span>\r\n          </div>\r\n          <div class=\"summary-row\">\r\n            <span>运费</span>\r\n            <span>免费</span>\r\n          </div>\r\n          <div class=\"summary-row total\">\r\n            <span>总计</span>\r\n            <span>¥{{ cartTotal }}</span>\r\n          </div>\r\n          <button class=\"checkout-button\">结账</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters, mapMutations } from 'vuex'\r\n\r\nexport default {\r\n  name: 'Bag',\r\n  computed: {\r\n    ...mapState(['cart']),\r\n    ...mapGetters(['cartTotal'])\r\n  },\r\n  methods: {\r\n    ...mapMutations(['removeFromCart'])\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.bag-page {\r\n  padding-top: 44px;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f7;\r\n}\r\n\r\n.bag-container {\r\n  max-width: 1024px;\r\n  margin: 0 auto;\r\n  padding: 40px 20px;\r\n}\r\n\r\nh1 {\r\n  font-size: 40px;\r\n  font-weight: 600;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.empty-bag {\r\n  text-align: center;\r\n  padding: 60px 0;\r\n}\r\n\r\n.empty-bag p {\r\n  font-size: 21px;\r\n  color: #86868b;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.continue-shopping {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 17px;\r\n}\r\n\r\n.continue-shopping:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.bag-content {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 40px;\r\n}\r\n\r\n.cart-items {\r\n  background: #fff;\r\n  border-radius: 18px;\r\n  padding: 20px;\r\n}\r\n\r\n.cart-item {\r\n  display: flex;\r\n  gap: 20px;\r\n  padding: 20px 0;\r\n  border-bottom: 1px solid #d2d2d7;\r\n}\r\n\r\n.cart-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  object-fit: contain;\r\n}\r\n\r\n.item-details {\r\n  flex: 1;\r\n}\r\n\r\n.item-details h3 {\r\n  font-size: 17px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.item-price {\r\n  color: #1d1d1f;\r\n  font-size: 17px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.remove-button {\r\n  background: none;\r\n  border: none;\r\n  color: #2997ff;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  padding: 0;\r\n}\r\n\r\n.remove-button:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.order-summary {\r\n  background: #fff;\r\n  border-radius: 18px;\r\n  padding: 20px;\r\n  height: fit-content;\r\n}\r\n\r\n.order-summary h2 {\r\n  font-size: 24px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  font-size: 17px;\r\n}\r\n\r\n.summary-row.total {\r\n  font-weight: 600;\r\n  font-size: 20px;\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #d2d2d7;\r\n}\r\n\r\n.checkout-button {\r\n  width: 100%;\r\n  background: #0071e3;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 980px;\r\n  padding: 18px 31px;\r\n  font-size: 17px;\r\n  font-weight: 400;\r\n  margin-top: 20px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.checkout-button:hover {\r\n  background: #0077ed;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .bag-content {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAe;;;EAGMA,KAAK,EAAC;;;;EAKxBA,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAY;;;EAGdA,KAAK,EAAC;AAAc;;EAEpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAc;;;EAO1BA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAa;;EAQnBA,KAAK,EAAC;AAAmB;;;uBAjCtCC,mBAAA,CAyCM,OAzCNC,UAyCM,GAxCJC,mBAAA,CAuCM,OAvCNC,UAuCM,G,0BAtCJD,mBAAA,CAAY,YAAR,KAAG,sBAEIE,IAAA,CAAAC,IAAI,CAACC,MAAM,U,cAAtBN,mBAAA,CAGM,OAHNO,UAGM,G,0BAFJL,mBAAA,CAAgB,WAAb,WAAS,sBACZM,YAAA,CAAqEC,sBAAA;IAAxDC,EAAE,EAAC,QAAQ;IAACX,KAAK,EAAC;;sBAAoB,MAAIY,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;yBAGzDX,mBAAA,CA8BM,OA9BNY,UA8BM,GA7BJV,mBAAA,CAWM,OAXNW,UAWM,I,kBAVJb,mBAAA,CASMc,SAAA,QAAAC,WAAA,CATcX,IAAA,CAAAC,IAAI,EAAZW,IAAI;yBAAhBhB,mBAAA,CASM;MATqBiB,GAAG,EAAED,IAAI,CAACE,EAAE;MAAEnB,KAAK,EAAC;QAC7CG,mBAAA,CAA6D;MAAvDiB,GAAG,EAAEH,IAAI,CAACI,KAAK;MAAGC,GAAG,EAAEL,IAAI,CAACM,IAAI;MAAEvB,KAAK,EAAC;yCAC9CG,mBAAA,CAMM,OANNqB,UAMM,GALJrB,mBAAA,CAAwB,YAAAsB,gBAAA,CAAjBR,IAAI,CAACM,IAAI,kBAChBpB,mBAAA,CAA2C,KAA3CuB,UAA2C,EAArB,GAAC,GAAAD,gBAAA,CAAGR,IAAI,CAACU,KAAK,kBACpCxB,mBAAA,CAEM,OAFNyB,UAEM,GADJzB,mBAAA,CAA0E;MAAlEH,KAAK,EAAC,eAAe;MAAE6B,OAAK,EAAAC,MAAA,IAAEzB,IAAA,CAAA0B,cAAc,CAACd,IAAI,CAACE,EAAE;OAAG,IAAE,iBAAAa,WAAA,E;oCAMzE7B,mBAAA,CAeM,OAfN8B,WAeM,G,0BAdJ9B,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAGM,OAHN+B,WAGM,G,0BAFJ/B,mBAAA,CAAe,cAAT,IAAE,sBACRA,mBAAA,CAA6B,cAAvB,GAAC,GAAAsB,gBAAA,CAAGpB,IAAA,CAAA8B,SAAS,iB,6BAErBhC,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAe,cAAT,IAAE,GACRA,mBAAA,CAAe,cAAT,IAAE,E,sBAEVA,mBAAA,CAGM,OAHNiC,WAGM,G,0BAFJjC,mBAAA,CAAe,cAAT,IAAE,sBACRA,mBAAA,CAA6B,cAAvB,GAAC,GAAAsB,gBAAA,CAAGpB,IAAA,CAAA8B,SAAS,iB,6BAErBhC,mBAAA,CAA2C;IAAnCH,KAAK,EAAC;EAAiB,GAAC,IAAE,qB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}