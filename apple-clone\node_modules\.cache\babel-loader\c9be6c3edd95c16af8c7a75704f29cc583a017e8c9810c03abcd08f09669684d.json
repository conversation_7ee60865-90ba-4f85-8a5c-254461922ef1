{"ast": null, "code": "export const products = [{\n  id: 1,\n  name: 'iPhone 15 Pro',\n  price: 7999,\n  image: '/images/iphone-15-pro.jpg',\n  description: '钛金属。超强大。超Pro。',\n  category: 'iphone'\n}, {\n  id: 2,\n  name: 'MacBook Air',\n  price: 7999,\n  image: '/images/macbook-air.jpg',\n  description: '搭载 M2 芯片',\n  category: 'mac'\n}, {\n  id: 3,\n  name: 'iPad Pro',\n  price: 6799,\n  image: '/images/ipad-pro.jpg',\n  description: '搭载 M2 芯片',\n  category: 'ipad'\n}];", "map": {"version": 3, "names": ["products", "id", "name", "price", "image", "description", "category"], "sources": ["E:/cursor/apple-clone/src/data/products.js"], "sourcesContent": ["export const products = [\r\n  {\r\n    id: 1,\r\n    name: 'iPhone 15 Pro',\r\n    price: 7999,\r\n    image: '/images/iphone-15-pro.jpg',\r\n    description: '钛金属。超强大。超Pro。',\r\n    category: 'iphone'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'MacBook Air',\r\n    price: 7999,\r\n    image: '/images/macbook-air.jpg',\r\n    description: '搭载 M2 芯片',\r\n    category: 'mac'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'iPad Pro',\r\n    price: 6799,\r\n    image: '/images/ipad-pro.jpg',\r\n    description: '搭载 M2 芯片',\r\n    category: 'ipad'\r\n  }\r\n] "], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,2BAA2B;EAClCC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE;AACZ,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}