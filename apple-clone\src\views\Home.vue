<template>
  <div class="home">
    <!-- 头部分区 -->
    <section id="head" class="titleBox">
      <div class="sticky-content" ref="headContent">
        <div class="hero-content">
          <h1 ref="headTitle">iPhone 15 Pro</h1>
          <h2 ref="headSubtitle">钛金属。超强大。超Pro。</h2>
          <div class="cta-links" ref="headLinks">
            <router-link to="/iphone-15-pro" class="link">了解更多</router-link>
            <router-link to="/shop/iphone-15-pro" class="link">购买</router-link>
          </div>
        </div>
        <div class="hero-image" ref="headImage">
          <img src="https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone+15+Pro" alt="iPhone 15 Pro" />
        </div>
      </div>
    </section>

    <!-- 介绍分区 -->
    <section id="intro" class="titleBox">
      <div class="sticky-content" ref="introContent">
        <div class="intro-section">
          <h2 ref="introTitle">创新科技</h2>
          <p ref="introText">探索Apple最新的技术突破，体验前所未有的性能与设计。</p>
          <div class="feature-grid" ref="introFeatures">
            <div class="feature-item">
              <h3>A17 Pro芯片</h3>
              <p>业界领先的3纳米工艺</p>
            </div>
            <div class="feature-item">
              <h3>钛金属设计</h3>
              <p>轻盈坚固，精工打造</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术分区 -->
    <section id="tech" class="titleBox">
      <div class="sticky-content" ref="techContent">
        <div class="tech-section">
          <h2 ref="techTitle">技术规格</h2>
          <div class="tech-grid" ref="techGrid">
            <div class="tech-item">
              <h3>显示屏</h3>
              <p>6.1英寸超视网膜XDR显示屏</p>
            </div>
            <div class="tech-item">
              <h3>摄像头</h3>
              <p>4800万像素主摄像头系统</p>
            </div>
            <div class="tech-item">
              <h3>电池</h3>
              <p>长达29小时视频播放</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 公司分区 -->
    <section id="company" class="titleBox">
      <div class="sticky-content" ref="companyContent">
        <div class="company-section">
          <h2 ref="companyTitle">Apple生态</h2>
          <div class="product-showcase" ref="companyProducts">
            <div class="product-item">
              <h3>MacBook Air</h3>
              <p>搭载 M2 芯片</p>
              <img src="https://via.placeholder.com/400x300/F5F5F7/000000?text=MacBook+Air" alt="MacBook Air" />
            </div>
            <div class="product-item">
              <h3>iPad Pro</h3>
              <p>搭载 M2 芯片</p>
              <img src="https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Pro" alt="iPad Pro" />
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      data: {
        sections: [],
        animateMomentInfo: {},
        scrollHeight: 0,
        clientHeight: 0
      }
    }
  },
  mounted() {
    this.initScrollAnimation()
  },
  beforeUnmount() {
    window.removeEventListener('scroll', this.handleScroll)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化滚动动画
    initScrollAnimation() {
      this.data.sections = ['head', 'intro', 'tech', 'company']
      this.updateScrollData()
      this.countAnimateMomentInfo()

      // 绑定滚动事件
      window.addEventListener('scroll', this.handleScroll)
      window.addEventListener('resize', this.handleResize)

      // 初始化动画状态
      this.handleScroll()
    },

    // 更新滚动相关数据
    updateScrollData() {
      this.data.scrollHeight = document.documentElement.scrollHeight
      this.data.clientHeight = document.documentElement.clientHeight
    },

    // 计算每个section的动画触发时机
    countAnimateMomentInfo() {
      for (const sectionId of this.data.sections) {
        const node = document.getElementById(sectionId)
        if (node) {
          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node)
        }
      }
    },

    // 根据section的高度，计算它在页面的位置
    blockAnimateStart(node) {
      const begin = this.countScrollRatio(node.offsetTop)
      const end = this.countScrollRatio(node.offsetTop + node.clientHeight)
      return { begin, end }
    },

    // 计算当前位置距离顶部高度占整个页面的百分比
    countScrollRatio(scrollTop) {
      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4))
    },

    // 滚动事件处理
    handleScroll() {
      const top = document.documentElement.scrollTop
      this.activateAnimate(this.countScrollRatio(top))
    },

    // 窗口大小改变事件处理
    handleResize() {
      this.updateScrollData()
      this.countAnimateMomentInfo()
    },

    // 激活动画
    activateAnimate(rate) {
      for (let key in this.data.animateMomentInfo) {
        const { begin, end } = this.data.animateMomentInfo[key]
        if (rate >= begin && rate <= end) {
          const progress = ((rate - begin) / (end - begin)).toFixed(3)
          this.executeAnimate(key, Math.min(1, Math.max(0, progress)))
        }
      }
    },

    // 执行具体的动画
    executeAnimate(id, rate) {
      switch (id) {
        case 'head':
          this.headAnimate(rate)
          break
        case 'intro':
          this.introAnimate(rate)
          break
        case 'tech':
          this.techAnimate(rate)
          break
        case 'company':
          this.companyAnimate(rate)
          break
        default:
          console.log('no action for', id)
      }
    },

    // 头部分区动画
    headAnimate(rate) {
      const title = this.$refs.headTitle
      const subtitle = this.$refs.headSubtitle
      const links = this.$refs.headLinks
      const image = this.$refs.headImage

      if (title) {
        title.style.transform = `translateY(${(1 - rate) * 50}px)`
        title.style.opacity = rate
      }
      if (subtitle) {
        subtitle.style.transform = `translateY(${(1 - rate) * 30}px)`
        subtitle.style.opacity = rate
      }
      if (links) {
        links.style.transform = `translateY(${(1 - rate) * 20}px)`
        links.style.opacity = rate
      }
      if (image) {
        image.style.transform = `scale(${0.8 + rate * 0.2}) translateX(${(1 - rate) * 100}px)`
        image.style.opacity = rate
      }
    },

    // 介绍分区动画
    introAnimate(rate) {
      const title = this.$refs.introTitle
      const text = this.$refs.introText
      const features = this.$refs.introFeatures

      if (title) {
        title.style.transform = `translateX(${(1 - rate) * -100}px)`
        title.style.opacity = rate
      }
      if (text) {
        text.style.transform = `translateX(${(1 - rate) * -50}px)`
        text.style.opacity = rate
      }
      if (features) {
        features.style.transform = `translateY(${(1 - rate) * 50}px)`
        features.style.opacity = rate
      }
    },

    // 技术分区动画
    techAnimate(rate) {
      const title = this.$refs.techTitle
      const grid = this.$refs.techGrid

      if (title) {
        title.style.transform = `scale(${0.5 + rate * 0.5})`
        title.style.opacity = rate
      }
      if (grid) {
        const items = grid.children
        for (let i = 0; i < items.length; i++) {
          const delay = i * 0.2
          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))
          items[i].style.transform = `translateY(${(1 - itemRate) * 30}px) rotateX(${(1 - itemRate) * 15}deg)`
          items[i].style.opacity = itemRate
        }
      }
    },

    // 公司分区动画
    companyAnimate(rate) {
      const title = this.$refs.companyTitle
      const products = this.$refs.companyProducts

      if (title) {
        title.style.transform = `translateY(${(1 - rate) * -30}px)`
        title.style.opacity = rate
      }
      if (products) {
        const items = products.children
        for (let i = 0; i < items.length; i++) {
          const delay = i * 0.3
          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))
          items[i].style.transform = `scale(${0.8 + itemRate * 0.2}) rotateY(${(1 - itemRate) * 10}deg)`
          items[i].style.opacity = itemRate
        }
      }
    }
  }
}
</script>

<style scoped>
.home {
  padding-top: 44px;
}

/* 分区基础样式 */
.titleBox {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.sticky-content {
  position: sticky;
  top: 0;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  transition: all 0.1s ease-out;
}

/* 头部分区样式 */
#head {
  background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
  color: #fff;
}

#head .sticky-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  max-width: 1200px;
  margin: 0 auto;
}

#head .hero-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
}

#head h1 {
  font-size: 64px;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.1;
  opacity: 0;
  transform: translateY(50px);
}

#head h2 {
  font-size: 32px;
  font-weight: 400;
  margin-bottom: 40px;
  color: #a1a1a6;
  opacity: 0;
  transform: translateY(30px);
}

.cta-links {
  display: flex;
  gap: 30px;
  opacity: 0;
  transform: translateY(20px);
}

.link {
  color: #2997ff;
  text-decoration: none;
  font-size: 21px;
  font-weight: 500;
  padding: 12px 24px;
  border: 1px solid #2997ff;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.link:hover {
  background-color: #2997ff;
  color: #fff;
}

.hero-image {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8) translateX(100px);
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
}

/* 介绍分区样式 */
#intro {
  background: linear-gradient(135deg, #f5f5f7 0%, #e8e8ed 100%);
  color: #1d1d1f;
}

.intro-section {
  max-width: 800px;
  text-align: center;
}

#intro h2 {
  font-size: 56px;
  font-weight: 700;
  margin-bottom: 30px;
  opacity: 0;
  transform: translateX(-100px);
}

#intro p {
  font-size: 24px;
  line-height: 1.6;
  margin-bottom: 50px;
  color: #6e6e73;
  opacity: 0;
  transform: translateX(-50px);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  opacity: 0;
  transform: translateY(50px);
}

.feature-item {
  background: #fff;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #1d1d1f;
}

.feature-item p {
  font-size: 16px;
  color: #6e6e73;
  margin: 0;
}

/* 技术分区样式 */
#tech {
  background: linear-gradient(135deg, #1d1d1f 0%, #2d2d30 100%);
  color: #fff;
}

.tech-section {
  max-width: 1000px;
  text-align: center;
}

#tech h2 {
  font-size: 56px;
  font-weight: 700;
  margin-bottom: 60px;
  opacity: 0;
  transform: scale(0.5);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.tech-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 40px 30px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0;
  transform: translateY(30px) rotateX(15deg);
  perspective: 1000px;
}

.tech-item h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #fff;
}

.tech-item p {
  font-size: 16px;
  color: #a1a1a6;
  margin: 0;
}

/* 公司分区样式 */
#company {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  color: #fff;
}

.company-section {
  max-width: 1000px;
  text-align: center;
}

#company h2 {
  font-size: 56px;
  font-weight: 700;
  margin-bottom: 60px;
  opacity: 0;
  transform: translateY(-30px);
}

.product-showcase {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
}

.product-item {
  background: rgba(255, 255, 255, 0.15);
  padding: 40px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  opacity: 0;
  transform: scale(0.8) rotateY(10deg);
  perspective: 1000px;
}

.product-item h3 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #fff;
}

.product-item p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.product-item img {
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  #head .sticky-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .product-showcase {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  #head h1 {
    font-size: 48px;
  }

  #head h2 {
    font-size: 24px;
  }

  #intro h2, #tech h2, #company h2 {
    font-size: 40px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .cta-links {
    flex-direction: column;
    align-items: center;
  }
}
</style>