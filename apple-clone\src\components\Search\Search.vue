<template>
  <div class="search-overlay" :class="{ active: isSearchOpen }">
    <div class="search-container">
      <div class="search-header">
        <div class="search-input-wrapper">
          <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="15" height="44" viewBox="0 0 15 44">
            <path d="M14.298,27.202l-3.87-3.87c0.701-0.929,1.122-2.081,1.122-3.332c0-3.06-2.489-5.55-5.55-5.55c-3.06,0-5.55,2.49-5.55,5.55 c0,3.061,2.49,5.55,5.55,5.55c1.251,0,2.403-0.421,3.332-1.122l3.87,3.87c0.151,0.151,0.35,0.228,0.548,0.228 s0.396-0.076,0.548-0.228C14.601,27.995,14.601,27.505,14.298,27.202z M1.55,20c0-2.454,1.997-4.45,4.45-4.45 c2.454,0,4.45,1.997,4.45,4.45S8.454,24.45,6,24.45C3.546,24.45,1.55,22.454,1.55,20z" fill="#86868b"/>
          </svg>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜索 apple.com.cn"
            @input="handleSearch"
          />
        </div>
        <button class="close-button" @click="closeSearch">取消</button>
      </div>
      
      <div class="search-results" v-if="searchQuery">
        <div class="quick-links">
          <h3>快速链接</h3>
          <div class="links-grid">
            <router-link to="/store" class="quick-link">访问 Apple Store 在线商店</router-link>
            <router-link to="/accessories" class="quick-link">配件</router-link>
            <router-link to="/airpods" class="quick-link">AirPods</router-link>
            <router-link to="/airtag" class="quick-link">AirTag</router-link>
            <router-link to="/apple-tv" class="quick-link">Apple TV</router-link>
            <router-link to="/ipad" class="quick-link">iPad</router-link>
            <router-link to="/iphone" class="quick-link">iPhone</router-link>
            <router-link to="/mac" class="quick-link">Mac</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'Search',
  data() {
    return {
      searchQuery: ''
    }
  },
  computed: {
    ...mapState(['isSearchOpen'])
  },
  methods: {
    ...mapMutations(['setSearchQuery', 'toggleSearch']),
    handleSearch() {
      this.setSearchQuery(this.searchQuery)
    },
    closeSearch() {
      this.toggleSearch()
      this.searchQuery = ''
      this.setSearchQuery('')
    }
  }
}
</script>

<style scoped>
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: saturate(180%) blur(20px);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.search-overlay.active {
  opacity: 1;
  visibility: visible;
}

.search-container {
  max-width: 1024px;
  margin: 0 auto;
  padding: 20px;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px 15px;
}

.search-icon {
  margin-right: 10px;
}

input {
  width: 100%;
  background: none;
  border: none;
  color: #fff;
  font-size: 17px;
  outline: none;
}

input::placeholder {
  color: #86868b;
}

.close-button {
  background: none;
  border: none;
  color: #fff;
  font-size: 17px;
  cursor: pointer;
  padding: 10px;
}

.search-results {
  color: #fff;
}

.quick-links h3 {
  font-size: 12px;
  color: #86868b;
  margin-bottom: 15px;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.quick-link {
  color: #2997ff;
  text-decoration: none;
  font-size: 14px;
}

.quick-link:hover {
  text-decoration: underline;
}
</style> 