{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Navbar = _resolveComponent(\"Navbar\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_Navbar), _createVNode(_component_router_view)]);\n}", "map": {"version": 3, "names": ["id", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_Navbar", "_component_router_view"], "sources": ["E:\\cursor\\apple-clone\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <Navbar />\r\n    <router-view></router-view>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Navbar from './components/Navbar/Navbar.vue'\r\n\r\nexport default {\r\n  name: 'App',\r\n  components: {\r\n    Navbar\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,\r\n    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background-color: #fff;\r\n}\r\n\r\n#app {\r\n  min-height: 100vh;\r\n}\r\n</style> "], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;;;uBAAbC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAAUC,iBAAA,GACVD,YAAA,CAA2BE,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}