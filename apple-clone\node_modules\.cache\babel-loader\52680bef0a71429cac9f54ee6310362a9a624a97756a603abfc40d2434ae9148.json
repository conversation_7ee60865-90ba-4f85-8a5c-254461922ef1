{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport Home from '../views/Home.vue';\nconst routes = [{\n  path: '/',\n  name: 'Home',\n  component: Home\n}, {\n  path: '/store',\n  name: 'Store',\n  component: () => import('../views/Store.vue')\n}, {\n  path: '/mac',\n  name: 'Mac',\n  component: () => import('../views/Mac.vue')\n}, {\n  path: '/ipad',\n  name: 'iPad',\n  component: () => import('../views/iPad.vue')\n}, {\n  path: '/iphone',\n  name: 'iPhone',\n  component: () => import('../views/iPhone.vue')\n}, {\n  path: '/watch',\n  name: 'Watch',\n  component: () => import('../views/Watch.vue')\n}, {\n  path: '/airpods',\n  name: 'AirPods',\n  component: () => import('../views/AirPods.vue')\n}, {\n  path: '/tv-home',\n  name: 'TVHome',\n  component: () => import('../views/TVHome.vue')\n}, {\n  path: '/entertainment',\n  name: 'Entertainment',\n  component: () => import('../views/Entertainment.vue')\n}, {\n  path: '/accessories',\n  name: 'Accessories',\n  component: () => import('../views/Accessories.vue')\n}, {\n  path: '/support',\n  name: 'Support',\n  component: () => import('../views/Support.vue')\n}, {\n  path: '/bag',\n  name: 'Bag',\n  component: () => import('../views/Bag.vue')\n}, {\n  path: '/animation-demo',\n  name: 'AnimationDemo',\n  component: () => import('../views/AnimationDemo.vue')\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "Home", "routes", "path", "name", "component", "router", "history", "process", "env", "BASE_URL"], "sources": ["E:/cursor/apple-clone/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport Home from '../views/Home.vue'\r\n\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'Home',\r\n    component: Home\r\n  },\r\n  {\r\n    path: '/store',\r\n    name: 'Store',\r\n    component: () => import('../views/Store.vue')\r\n  },\r\n  {\r\n    path: '/mac',\r\n    name: 'Mac',\r\n    component: () => import('../views/Mac.vue')\r\n  },\r\n  {\r\n    path: '/ipad',\r\n    name: 'iPad',\r\n    component: () => import('../views/iPad.vue')\r\n  },\r\n  {\r\n    path: '/iphone',\r\n    name: 'iPhone',\r\n    component: () => import('../views/iPhone.vue')\r\n  },\r\n  {\r\n    path: '/watch',\r\n    name: 'Watch',\r\n    component: () => import('../views/Watch.vue')\r\n  },\r\n  {\r\n    path: '/airpods',\r\n    name: 'AirPods',\r\n    component: () => import('../views/AirPods.vue')\r\n  },\r\n  {\r\n    path: '/tv-home',\r\n    name: 'TVHome',\r\n    component: () => import('../views/TVHome.vue')\r\n  },\r\n  {\r\n    path: '/entertainment',\r\n    name: 'Entertainment',\r\n    component: () => import('../views/Entertainment.vue')\r\n  },\r\n  {\r\n    path: '/accessories',\r\n    name: 'Accessories',\r\n    component: () => import('../views/Accessories.vue')\r\n  },\r\n  {\r\n    path: '/support',\r\n    name: 'Support',\r\n    component: () => import('../views/Support.vue')\r\n  },\r\n  {\r\n    path: '/bag',\r\n    name: 'Bag',\r\n    component: () => import('../views/Bag.vue')\r\n  },\r\n  {\r\n    path: '/animation-demo',\r\n    name: 'AnimationDemo',\r\n    component: () => import('../views/AnimationDemo.vue')\r\n  }\r\n]\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes\r\n})\r\n\r\nexport default router "], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,IAAI,MAAM,mBAAmB;AAEpC,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEJ;AACb,CAAC,EACD;EACEE,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB;AAC9C,CAAC,EACD;EACEF,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB;AAC5C,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB;AAC7C,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB;AAC/C,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB;AAC9C,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB;AAChD,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB;AAC/C,CAAC,EACD;EACEF,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B;AACtD,CAAC,EACD;EACEF,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B;AACpD,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB;AAChD,CAAC,EACD;EACEF,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB;AAC5C,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B;AACtD,CAAC,CACF;AAED,MAAMC,MAAM,GAAGP,YAAY,CAAC;EAC1BQ,OAAO,EAAEP,gBAAgB,CAACQ,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CR;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}