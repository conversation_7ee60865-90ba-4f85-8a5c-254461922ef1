{"ast": null, "code": "export default {\n  name: 'Home',\n  data() {\n    return {\n      data: {\n        sections: [],\n        animateMomentInfo: {},\n        scrollHeight: 0,\n        clientHeight: 0\n      }\n    };\n  },\n  mounted() {\n    this.initScrollAnimation();\n  },\n  beforeUnmount() {\n    window.removeEventListener('scroll', this.handleScroll);\n    window.removeEventListener('resize', this.handleResize);\n  },\n  methods: {\n    // 初始化滚动动画\n    initScrollAnimation() {\n      this.data.sections = ['head', 'intro', 'tech', 'company'];\n      this.updateScrollData();\n      this.countAnimateMomentInfo();\n\n      // 绑定滚动事件\n      window.addEventListener('scroll', this.handleScroll);\n      window.addEventListener('resize', this.handleResize);\n\n      // 初始化动画状态\n      this.handleScroll();\n    },\n    // 更新滚动相关数据\n    updateScrollData() {\n      this.data.scrollHeight = document.documentElement.scrollHeight;\n      this.data.clientHeight = document.documentElement.clientHeight;\n    },\n    // 计算每个section的动画触发时机\n    countAnimateMomentInfo() {\n      for (const sectionId of this.data.sections) {\n        const node = document.getElementById(sectionId);\n        if (node) {\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node);\n        }\n      }\n    },\n    // 根据section的高度，计算它在页面的位置\n    blockAnimateStart(node) {\n      const begin = this.countScrollRatio(node.offsetTop);\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight);\n      return {\n        begin,\n        end\n      };\n    },\n    // 计算当前位置距离顶部高度占整个页面的百分比\n    countScrollRatio(scrollTop) {\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4));\n    },\n    // 滚动事件处理\n    handleScroll() {\n      const top = document.documentElement.scrollTop;\n      this.activateAnimate(this.countScrollRatio(top));\n    },\n    // 窗口大小改变事件处理\n    handleResize() {\n      this.updateScrollData();\n      this.countAnimateMomentInfo();\n    },\n    // 激活动画\n    activateAnimate(rate) {\n      for (let key in this.data.animateMomentInfo) {\n        const {\n          begin,\n          end\n        } = this.data.animateMomentInfo[key];\n        if (rate >= begin && rate <= end) {\n          const progress = ((rate - begin) / (end - begin)).toFixed(3);\n          this.executeAnimate(key, Math.min(1, Math.max(0, progress)));\n        }\n      }\n    },\n    // 执行具体的动画\n    executeAnimate(id, rate) {\n      switch (id) {\n        case 'head':\n          this.headAnimate(rate);\n          break;\n        case 'intro':\n          this.introAnimate(rate);\n          break;\n        case 'tech':\n          this.techAnimate(rate);\n          break;\n        case 'company':\n          this.companyAnimate(rate);\n          break;\n        default:\n          console.log('no action for', id);\n      }\n    },\n    // 头部分区动画\n    headAnimate(rate) {\n      const title = this.$refs.headTitle;\n      const subtitle = this.$refs.headSubtitle;\n      const links = this.$refs.headLinks;\n      const image = this.$refs.headImage;\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * 50}px)`;\n        title.style.opacity = rate;\n      }\n      if (subtitle) {\n        subtitle.style.transform = `translateY(${(1 - rate) * 30}px)`;\n        subtitle.style.opacity = rate;\n      }\n      if (links) {\n        links.style.transform = `translateY(${(1 - rate) * 20}px)`;\n        links.style.opacity = rate;\n      }\n      if (image) {\n        image.style.transform = `scale(${0.8 + rate * 0.2}) translateX(${(1 - rate) * 100}px)`;\n        image.style.opacity = rate;\n      }\n    },\n    // 介绍分区动画\n    introAnimate(rate) {\n      const title = this.$refs.introTitle;\n      const text = this.$refs.introText;\n      const features = this.$refs.introFeatures;\n      if (title) {\n        title.style.transform = `translateX(${(1 - rate) * -100}px)`;\n        title.style.opacity = rate;\n      }\n      if (text) {\n        text.style.transform = `translateX(${(1 - rate) * -50}px)`;\n        text.style.opacity = rate;\n      }\n      if (features) {\n        features.style.transform = `translateY(${(1 - rate) * 50}px)`;\n        features.style.opacity = rate;\n      }\n    },\n    // 技术分区动画\n    techAnimate(rate) {\n      const title = this.$refs.techTitle;\n      const grid = this.$refs.techGrid;\n      if (title) {\n        title.style.transform = `scale(${0.5 + rate * 0.5})`;\n        title.style.opacity = rate;\n      }\n      if (grid) {\n        const items = grid.children;\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.2;\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)));\n          items[i].style.transform = `translateY(${(1 - itemRate) * 30}px) rotateX(${(1 - itemRate) * 15}deg)`;\n          items[i].style.opacity = itemRate;\n        }\n      }\n    },\n    // 公司分区动画\n    companyAnimate(rate) {\n      const title = this.$refs.companyTitle;\n      const products = this.$refs.companyProducts;\n      if (title) {\n        title.style.transform = `translateY(${(1 - rate) * -30}px)`;\n        title.style.opacity = rate;\n      }\n      if (products) {\n        const items = products.children;\n        for (let i = 0; i < items.length; i++) {\n          const delay = i * 0.3;\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)));\n          items[i].style.transform = `scale(${0.8 + itemRate * 0.2}) rotateY(${(1 - itemRate) * 10}deg)`;\n          items[i].style.opacity = itemRate;\n        }\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "sections", "animateMomentInfo", "scrollHeight", "clientHeight", "mounted", "initScrollAnimation", "beforeUnmount", "window", "removeEventListener", "handleScroll", "handleResize", "methods", "updateScrollData", "countAnimateMomentInfo", "addEventListener", "document", "documentElement", "sectionId", "node", "getElementById", "blockAnimateStart", "begin", "countScrollRatio", "offsetTop", "end", "scrollTop", "Number", "toFixed", "top", "activateAnimate", "rate", "key", "progress", "executeAnimate", "Math", "min", "max", "id", "headAnimate", "introAnimate", "techAnimate", "companyAnimate", "console", "log", "title", "$refs", "head<PERSON><PERSON>le", "subtitle", "headSubtitle", "links", "headLinks", "image", "headImage", "style", "transform", "opacity", "introTitle", "text", "introText", "features", "introFeatures", "techTitle", "grid", "techGrid", "items", "children", "i", "length", "delay", "itemRate", "companyTitle", "products", "companyProducts"], "sources": ["E:\\cursor\\apple-clone\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <!-- 头部分区 -->\r\n    <section id=\"head\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"headContent\">\r\n        <div class=\"hero-content\">\r\n          <h1 ref=\"headTitle\">iPhone 15 Pro</h1>\r\n          <h2 ref=\"headSubtitle\">钛金属。超强大。超Pro。</h2>\r\n          <div class=\"cta-links\" ref=\"headLinks\">\r\n            <router-link to=\"/iphone-15-pro\" class=\"link\">了解更多</router-link>\r\n            <router-link to=\"/shop/iphone-15-pro\" class=\"link\">购买</router-link>\r\n          </div>\r\n        </div>\r\n        <div class=\"hero-image\" ref=\"headImage\">\r\n          <img src=\"https://via.placeholder.com/600x400/000000/FFFFFF?text=iPhone+15+Pro\" alt=\"iPhone 15 Pro\" />\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 介绍分区 -->\r\n    <section id=\"intro\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"introContent\">\r\n        <div class=\"intro-section\">\r\n          <h2 ref=\"introTitle\">创新科技</h2>\r\n          <p ref=\"introText\">探索Apple最新的技术突破，体验前所未有的性能与设计。</p>\r\n          <div class=\"feature-grid\" ref=\"introFeatures\">\r\n            <div class=\"feature-item\">\r\n              <h3>A17 Pro芯片</h3>\r\n              <p>业界领先的3纳米工艺</p>\r\n            </div>\r\n            <div class=\"feature-item\">\r\n              <h3>钛金属设计</h3>\r\n              <p>轻盈坚固，精工打造</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 技术分区 -->\r\n    <section id=\"tech\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"techContent\">\r\n        <div class=\"tech-section\">\r\n          <h2 ref=\"techTitle\">技术规格</h2>\r\n          <div class=\"tech-grid\" ref=\"techGrid\">\r\n            <div class=\"tech-item\">\r\n              <h3>显示屏</h3>\r\n              <p>6.1英寸超视网膜XDR显示屏</p>\r\n            </div>\r\n            <div class=\"tech-item\">\r\n              <h3>摄像头</h3>\r\n              <p>4800万像素主摄像头系统</p>\r\n            </div>\r\n            <div class=\"tech-item\">\r\n              <h3>电池</h3>\r\n              <p>长达29小时视频播放</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 公司分区 -->\r\n    <section id=\"company\" class=\"titleBox\">\r\n      <div class=\"sticky-content\" ref=\"companyContent\">\r\n        <div class=\"company-section\">\r\n          <h2 ref=\"companyTitle\">Apple生态</h2>\r\n          <div class=\"product-showcase\" ref=\"companyProducts\">\r\n            <div class=\"product-item\">\r\n              <h3>MacBook Air</h3>\r\n              <p>搭载 M2 芯片</p>\r\n              <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=MacBook+Air\" alt=\"MacBook Air\" />\r\n            </div>\r\n            <div class=\"product-item\">\r\n              <h3>iPad Pro</h3>\r\n              <p>搭载 M2 芯片</p>\r\n              <img src=\"https://via.placeholder.com/400x300/F5F5F7/000000?text=iPad+Pro\" alt=\"iPad Pro\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      data: {\r\n        sections: [],\r\n        animateMomentInfo: {},\r\n        scrollHeight: 0,\r\n        clientHeight: 0\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initScrollAnimation()\r\n  },\r\n  beforeUnmount() {\r\n    window.removeEventListener('scroll', this.handleScroll)\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  methods: {\r\n    // 初始化滚动动画\r\n    initScrollAnimation() {\r\n      this.data.sections = ['head', 'intro', 'tech', 'company']\r\n      this.updateScrollData()\r\n      this.countAnimateMomentInfo()\r\n\r\n      // 绑定滚动事件\r\n      window.addEventListener('scroll', this.handleScroll)\r\n      window.addEventListener('resize', this.handleResize)\r\n\r\n      // 初始化动画状态\r\n      this.handleScroll()\r\n    },\r\n\r\n    // 更新滚动相关数据\r\n    updateScrollData() {\r\n      this.data.scrollHeight = document.documentElement.scrollHeight\r\n      this.data.clientHeight = document.documentElement.clientHeight\r\n    },\r\n\r\n    // 计算每个section的动画触发时机\r\n    countAnimateMomentInfo() {\r\n      for (const sectionId of this.data.sections) {\r\n        const node = document.getElementById(sectionId)\r\n        if (node) {\r\n          this.data.animateMomentInfo[sectionId] = this.blockAnimateStart(node)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 根据section的高度，计算它在页面的位置\r\n    blockAnimateStart(node) {\r\n      const begin = this.countScrollRatio(node.offsetTop)\r\n      const end = this.countScrollRatio(node.offsetTop + node.clientHeight)\r\n      return { begin, end }\r\n    },\r\n\r\n    // 计算当前位置距离顶部高度占整个页面的百分比\r\n    countScrollRatio(scrollTop) {\r\n      return Number((100 * scrollTop / (this.data.scrollHeight - this.data.clientHeight)).toFixed(4))\r\n    },\r\n\r\n    // 滚动事件处理\r\n    handleScroll() {\r\n      const top = document.documentElement.scrollTop\r\n      this.activateAnimate(this.countScrollRatio(top))\r\n    },\r\n\r\n    // 窗口大小改变事件处理\r\n    handleResize() {\r\n      this.updateScrollData()\r\n      this.countAnimateMomentInfo()\r\n    },\r\n\r\n    // 激活动画\r\n    activateAnimate(rate) {\r\n      for (let key in this.data.animateMomentInfo) {\r\n        const { begin, end } = this.data.animateMomentInfo[key]\r\n        if (rate >= begin && rate <= end) {\r\n          const progress = ((rate - begin) / (end - begin)).toFixed(3)\r\n          this.executeAnimate(key, Math.min(1, Math.max(0, progress)))\r\n        }\r\n      }\r\n    },\r\n\r\n    // 执行具体的动画\r\n    executeAnimate(id, rate) {\r\n      switch (id) {\r\n        case 'head':\r\n          this.headAnimate(rate)\r\n          break\r\n        case 'intro':\r\n          this.introAnimate(rate)\r\n          break\r\n        case 'tech':\r\n          this.techAnimate(rate)\r\n          break\r\n        case 'company':\r\n          this.companyAnimate(rate)\r\n          break\r\n        default:\r\n          console.log('no action for', id)\r\n      }\r\n    },\r\n\r\n    // 头部分区动画\r\n    headAnimate(rate) {\r\n      const title = this.$refs.headTitle\r\n      const subtitle = this.$refs.headSubtitle\r\n      const links = this.$refs.headLinks\r\n      const image = this.$refs.headImage\r\n\r\n      if (title) {\r\n        title.style.transform = `translateY(${(1 - rate) * 50}px)`\r\n        title.style.opacity = rate\r\n      }\r\n      if (subtitle) {\r\n        subtitle.style.transform = `translateY(${(1 - rate) * 30}px)`\r\n        subtitle.style.opacity = rate\r\n      }\r\n      if (links) {\r\n        links.style.transform = `translateY(${(1 - rate) * 20}px)`\r\n        links.style.opacity = rate\r\n      }\r\n      if (image) {\r\n        image.style.transform = `scale(${0.8 + rate * 0.2}) translateX(${(1 - rate) * 100}px)`\r\n        image.style.opacity = rate\r\n      }\r\n    },\r\n\r\n    // 介绍分区动画\r\n    introAnimate(rate) {\r\n      const title = this.$refs.introTitle\r\n      const text = this.$refs.introText\r\n      const features = this.$refs.introFeatures\r\n\r\n      if (title) {\r\n        title.style.transform = `translateX(${(1 - rate) * -100}px)`\r\n        title.style.opacity = rate\r\n      }\r\n      if (text) {\r\n        text.style.transform = `translateX(${(1 - rate) * -50}px)`\r\n        text.style.opacity = rate\r\n      }\r\n      if (features) {\r\n        features.style.transform = `translateY(${(1 - rate) * 50}px)`\r\n        features.style.opacity = rate\r\n      }\r\n    },\r\n\r\n    // 技术分区动画\r\n    techAnimate(rate) {\r\n      const title = this.$refs.techTitle\r\n      const grid = this.$refs.techGrid\r\n\r\n      if (title) {\r\n        title.style.transform = `scale(${0.5 + rate * 0.5})`\r\n        title.style.opacity = rate\r\n      }\r\n      if (grid) {\r\n        const items = grid.children\r\n        for (let i = 0; i < items.length; i++) {\r\n          const delay = i * 0.2\r\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))\r\n          items[i].style.transform = `translateY(${(1 - itemRate) * 30}px) rotateX(${(1 - itemRate) * 15}deg)`\r\n          items[i].style.opacity = itemRate\r\n        }\r\n      }\r\n    },\r\n\r\n    // 公司分区动画\r\n    companyAnimate(rate) {\r\n      const title = this.$refs.companyTitle\r\n      const products = this.$refs.companyProducts\r\n\r\n      if (title) {\r\n        title.style.transform = `translateY(${(1 - rate) * -30}px)`\r\n        title.style.opacity = rate\r\n      }\r\n      if (products) {\r\n        const items = products.children\r\n        for (let i = 0; i < items.length; i++) {\r\n          const delay = i * 0.3\r\n          const itemRate = Math.max(0, Math.min(1, (rate - delay) / (1 - delay)))\r\n          items[i].style.transform = `scale(${0.8 + itemRate * 0.2}) rotateY(${(1 - itemRate) * 10}deg)`\r\n          items[i].style.opacity = itemRate\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding-top: 44px;\r\n}\r\n\r\n/* 分区基础样式 */\r\n.titleBox {\r\n  height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.sticky-content {\r\n  position: sticky;\r\n  top: 0;\r\n  height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0 20px;\r\n  transition: all 0.1s ease-out;\r\n}\r\n\r\n/* 头部分区样式 */\r\n#head {\r\n  background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);\r\n  color: #fff;\r\n}\r\n\r\n#head .sticky-content {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 60px;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n#head .hero-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  text-align: left;\r\n}\r\n\r\n#head h1 {\r\n  font-size: 64px;\r\n  font-weight: 700;\r\n  margin-bottom: 20px;\r\n  line-height: 1.1;\r\n  opacity: 0;\r\n  transform: translateY(50px);\r\n}\r\n\r\n#head h2 {\r\n  font-size: 32px;\r\n  font-weight: 400;\r\n  margin-bottom: 40px;\r\n  color: #a1a1a6;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n.cta-links {\r\n  display: flex;\r\n  gap: 30px;\r\n  opacity: 0;\r\n  transform: translateY(20px);\r\n}\r\n\r\n.link {\r\n  color: #2997ff;\r\n  text-decoration: none;\r\n  font-size: 21px;\r\n  font-weight: 500;\r\n  padding: 12px 24px;\r\n  border: 1px solid #2997ff;\r\n  border-radius: 25px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.link:hover {\r\n  background-color: #2997ff;\r\n  color: #fff;\r\n}\r\n\r\n.hero-image {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transform: scale(0.8) translateX(100px);\r\n}\r\n\r\n.hero-image img {\r\n  max-width: 100%;\r\n  height: auto;\r\n  border-radius: 20px;\r\n}\r\n\r\n/* 介绍分区样式 */\r\n#intro {\r\n  background: linear-gradient(135deg, #f5f5f7 0%, #e8e8ed 100%);\r\n  color: #1d1d1f;\r\n}\r\n\r\n.intro-section {\r\n  max-width: 800px;\r\n  text-align: center;\r\n}\r\n\r\n#intro h2 {\r\n  font-size: 56px;\r\n  font-weight: 700;\r\n  margin-bottom: 30px;\r\n  opacity: 0;\r\n  transform: translateX(-100px);\r\n}\r\n\r\n#intro p {\r\n  font-size: 24px;\r\n  line-height: 1.6;\r\n  margin-bottom: 50px;\r\n  color: #6e6e73;\r\n  opacity: 0;\r\n  transform: translateX(-50px);\r\n}\r\n\r\n.feature-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 40px;\r\n  opacity: 0;\r\n  transform: translateY(50px);\r\n}\r\n\r\n.feature-item {\r\n  background: #fff;\r\n  padding: 30px;\r\n  border-radius: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.feature-item h3 {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n  color: #1d1d1f;\r\n}\r\n\r\n.feature-item p {\r\n  font-size: 16px;\r\n  color: #6e6e73;\r\n  margin: 0;\r\n}\r\n</style>"], "mappings": "AAsFA,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLA,IAAI,EAAE;QACJC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,CAAC,CAAC;QACrBC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;MAChB;IACF;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,mBAAmB,CAAC;EAC3B,CAAC;EACDC,aAAaA,CAAA,EAAG;IACdC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY;IACtDF,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACE,YAAY;EACxD,CAAC;EACDC,OAAO,EAAE;IACP;IACAN,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACN,IAAI,CAACC,QAAO,GAAI,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS;MACxD,IAAI,CAACY,gBAAgB,CAAC;MACtB,IAAI,CAACC,sBAAsB,CAAC;;MAE5B;MACAN,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACL,YAAY;MACnDF,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACJ,YAAY;;MAEnD;MACA,IAAI,CAACD,YAAY,CAAC;IACpB,CAAC;IAED;IACAG,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACb,IAAI,CAACG,YAAW,GAAIa,QAAQ,CAACC,eAAe,CAACd,YAAW;MAC7D,IAAI,CAACH,IAAI,CAACI,YAAW,GAAIY,QAAQ,CAACC,eAAe,CAACb,YAAW;IAC/D,CAAC;IAED;IACAU,sBAAsBA,CAAA,EAAG;MACvB,KAAK,MAAMI,SAAQ,IAAK,IAAI,CAAClB,IAAI,CAACC,QAAQ,EAAE;QAC1C,MAAMkB,IAAG,GAAIH,QAAQ,CAACI,cAAc,CAACF,SAAS;QAC9C,IAAIC,IAAI,EAAE;UACR,IAAI,CAACnB,IAAI,CAACE,iBAAiB,CAACgB,SAAS,IAAI,IAAI,CAACG,iBAAiB,CAACF,IAAI;QACtE;MACF;IACF,CAAC;IAED;IACAE,iBAAiBA,CAACF,IAAI,EAAE;MACtB,MAAMG,KAAI,GAAI,IAAI,CAACC,gBAAgB,CAACJ,IAAI,CAACK,SAAS;MAClD,MAAMC,GAAE,GAAI,IAAI,CAACF,gBAAgB,CAACJ,IAAI,CAACK,SAAQ,GAAIL,IAAI,CAACf,YAAY;MACpE,OAAO;QAAEkB,KAAK;QAAEG;MAAI;IACtB,CAAC;IAED;IACAF,gBAAgBA,CAACG,SAAS,EAAE;MAC1B,OAAOC,MAAM,CAAC,CAAC,GAAE,GAAID,SAAQ,IAAK,IAAI,CAAC1B,IAAI,CAACG,YAAW,GAAI,IAAI,CAACH,IAAI,CAACI,YAAY,CAAC,EAAEwB,OAAO,CAAC,CAAC,CAAC;IAChG,CAAC;IAED;IACAlB,YAAYA,CAAA,EAAG;MACb,MAAMmB,GAAE,GAAIb,QAAQ,CAACC,eAAe,CAACS,SAAQ;MAC7C,IAAI,CAACI,eAAe,CAAC,IAAI,CAACP,gBAAgB,CAACM,GAAG,CAAC;IACjD,CAAC;IAED;IACAlB,YAAYA,CAAA,EAAG;MACb,IAAI,CAACE,gBAAgB,CAAC;MACtB,IAAI,CAACC,sBAAsB,CAAC;IAC9B,CAAC;IAED;IACAgB,eAAeA,CAACC,IAAI,EAAE;MACpB,KAAK,IAAIC,GAAE,IAAK,IAAI,CAAChC,IAAI,CAACE,iBAAiB,EAAE;QAC3C,MAAM;UAAEoB,KAAK;UAAEG;QAAI,IAAI,IAAI,CAACzB,IAAI,CAACE,iBAAiB,CAAC8B,GAAG;QACtD,IAAID,IAAG,IAAKT,KAAI,IAAKS,IAAG,IAAKN,GAAG,EAAE;UAChC,MAAMQ,QAAO,GAAI,CAAC,CAACF,IAAG,GAAIT,KAAK,KAAKG,GAAE,GAAIH,KAAK,CAAC,EAAEM,OAAO,CAAC,CAAC;UAC3D,IAAI,CAACM,cAAc,CAACF,GAAG,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,QAAQ,CAAC,CAAC;QAC7D;MACF;IACF,CAAC;IAED;IACAC,cAAcA,CAACI,EAAE,EAAEP,IAAI,EAAE;MACvB,QAAQO,EAAE;QACR,KAAK,MAAM;UACT,IAAI,CAACC,WAAW,CAACR,IAAI;UACrB;QACF,KAAK,OAAO;UACV,IAAI,CAACS,YAAY,CAACT,IAAI;UACtB;QACF,KAAK,MAAM;UACT,IAAI,CAACU,WAAW,CAACV,IAAI;UACrB;QACF,KAAK,SAAS;UACZ,IAAI,CAACW,cAAc,CAACX,IAAI;UACxB;QACF;UACEY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEN,EAAE;MACnC;IACF,CAAC;IAED;IACAC,WAAWA,CAACR,IAAI,EAAE;MAChB,MAAMc,KAAI,GAAI,IAAI,CAACC,KAAK,CAACC,SAAQ;MACjC,MAAMC,QAAO,GAAI,IAAI,CAACF,KAAK,CAACG,YAAW;MACvC,MAAMC,KAAI,GAAI,IAAI,CAACJ,KAAK,CAACK,SAAQ;MACjC,MAAMC,KAAI,GAAI,IAAI,CAACN,KAAK,CAACO,SAAQ;MAEjC,IAAIR,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIxB,IAAI,IAAI,EAAE,KAAI;QACzDc,KAAK,CAACS,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC3B;MACA,IAAIiB,QAAQ,EAAE;QACZA,QAAQ,CAACM,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIxB,IAAI,IAAI,EAAE,KAAI;QAC5DiB,QAAQ,CAACM,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC9B;MACA,IAAImB,KAAK,EAAE;QACTA,KAAK,CAACI,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIxB,IAAI,IAAI,EAAE,KAAI;QACzDmB,KAAK,CAACI,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC3B;MACA,IAAIqB,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,CAACC,SAAQ,GAAI,SAAS,GAAE,GAAIxB,IAAG,GAAI,GAAG,gBAAgB,CAAC,IAAIA,IAAI,IAAI,GAAG,KAAI;QACrFqB,KAAK,CAACE,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC3B;IACF,CAAC;IAED;IACAS,YAAYA,CAACT,IAAI,EAAE;MACjB,MAAMc,KAAI,GAAI,IAAI,CAACC,KAAK,CAACW,UAAS;MAClC,MAAMC,IAAG,GAAI,IAAI,CAACZ,KAAK,CAACa,SAAQ;MAChC,MAAMC,QAAO,GAAI,IAAI,CAACd,KAAK,CAACe,aAAY;MAExC,IAAIhB,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIxB,IAAI,IAAI,CAAC,GAAG,KAAI;QAC3Dc,KAAK,CAACS,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC3B;MACA,IAAI2B,IAAI,EAAE;QACRA,IAAI,CAACJ,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIxB,IAAI,IAAI,CAAC,EAAE,KAAI;QACzD2B,IAAI,CAACJ,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC1B;MACA,IAAI6B,QAAQ,EAAE;QACZA,QAAQ,CAACN,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIxB,IAAI,IAAI,EAAE,KAAI;QAC5D6B,QAAQ,CAACN,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC9B;IACF,CAAC;IAED;IACAU,WAAWA,CAACV,IAAI,EAAE;MAChB,MAAMc,KAAI,GAAI,IAAI,CAACC,KAAK,CAACgB,SAAQ;MACjC,MAAMC,IAAG,GAAI,IAAI,CAACjB,KAAK,CAACkB,QAAO;MAE/B,IAAInB,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,SAAS,GAAE,GAAIxB,IAAG,GAAI,GAAG,GAAE;QACnDc,KAAK,CAACS,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC3B;MACA,IAAIgC,IAAI,EAAE;QACR,MAAME,KAAI,GAAIF,IAAI,CAACG,QAAO;QAC1B,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACrC,MAAME,KAAI,GAAIF,CAAA,GAAI,GAAE;UACpB,MAAMG,QAAO,GAAInC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACL,IAAG,GAAIsC,KAAK,KAAK,IAAIA,KAAK,CAAC,CAAC;UACtEJ,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIe,QAAQ,IAAI,EAAE,eAAe,CAAC,IAAIA,QAAQ,IAAI,EAAE,MAAK;UACnGL,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACE,OAAM,GAAIc,QAAO;QAClC;MACF;IACF,CAAC;IAED;IACA5B,cAAcA,CAACX,IAAI,EAAE;MACnB,MAAMc,KAAI,GAAI,IAAI,CAACC,KAAK,CAACyB,YAAW;MACpC,MAAMC,QAAO,GAAI,IAAI,CAAC1B,KAAK,CAAC2B,eAAc;MAE1C,IAAI5B,KAAK,EAAE;QACTA,KAAK,CAACS,KAAK,CAACC,SAAQ,GAAI,cAAc,CAAC,IAAIxB,IAAI,IAAI,CAAC,EAAE,KAAI;QAC1Dc,KAAK,CAACS,KAAK,CAACE,OAAM,GAAIzB,IAAG;MAC3B;MACA,IAAIyC,QAAQ,EAAE;QACZ,MAAMP,KAAI,GAAIO,QAAQ,CAACN,QAAO;QAC9B,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACrC,MAAME,KAAI,GAAIF,CAAA,GAAI,GAAE;UACpB,MAAMG,QAAO,GAAInC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACL,IAAG,GAAIsC,KAAK,KAAK,IAAIA,KAAK,CAAC,CAAC;UACtEJ,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACC,SAAQ,GAAI,SAAS,GAAE,GAAIe,QAAO,GAAI,GAAG,aAAa,CAAC,IAAIA,QAAQ,IAAI,EAAE,MAAK;UAC7FL,KAAK,CAACE,CAAC,CAAC,CAACb,KAAK,CAACE,OAAM,GAAIc,QAAO;QAClC;MACF;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}