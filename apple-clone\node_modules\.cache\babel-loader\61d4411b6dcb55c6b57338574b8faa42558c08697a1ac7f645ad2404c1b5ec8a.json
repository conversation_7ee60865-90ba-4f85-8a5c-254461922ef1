{"ast": null, "code": "import Navbar from './components/Navbar/Navbar.vue';\nexport default {\n  name: 'App',\n  components: {\n    Navbar\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "name", "components"], "sources": ["E:\\cursor\\apple-clone\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <Navbar />\r\n    <router-view></router-view>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Navbar from './components/Navbar/Navbar.vue'\r\n\r\nexport default {\r\n  name: 'App',\r\n  components: {\r\n    Navbar\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,\r\n    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background-color: #fff;\r\n}\r\n\r\n#app {\r\n  min-height: 100vh;\r\n}\r\n</style> "], "mappings": "AAQA,OAAOA,MAAK,MAAO,gCAA+B;AAElD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}